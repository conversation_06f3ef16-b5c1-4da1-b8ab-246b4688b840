{"name": "k12-admin-backend", "version": "1.0.0", "description": "K12教育资源管理后台 - 后端服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "node test-permissions.js"}, "dependencies": {"@cloudbase/node-sdk": "^3.10.1", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "iconv-lite": "^0.6.3", "joi": "^17.11.0", "mammoth": "^1.6.0", "multer": "^1.4.5-lts.1", "pdf-poppler": "^0.2.1", "sharp": "^0.33.0", "uuid": "^9.0.0", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["k12", "education", "admin", "cloudbase"], "author": "K12 Admin Team", "license": "MIT"}