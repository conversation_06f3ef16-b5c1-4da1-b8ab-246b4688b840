<template>
  <div class="file-management">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="header-left">
        <h2>📁 文件管理</h2>
        <p class="header-desc">管理教育资源文件，支持批量上传和CSV导入</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showUploadDialog = true">
          <el-icon><Upload /></el-icon>
          上传文件
        </el-button>
        <el-button type="success" @click="showCsvUploadDialog = true">
          <el-icon><Document /></el-icon>
          CSV批量上传
        </el-button>
        <el-button @click="downloadCsvTemplate">
          <el-icon><Download /></el-icon>
          下载模板
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-row">
        <div class="filter-item">
          <label>年级：</label>
          <el-select v-model="filters.grade" placeholder="选择年级" clearable @change="handleFilterChange">
            <el-option label="全部" value="" />
            <el-option v-for="grade in filterOptions.grades" :key="grade" :label="grade" :value="grade" />
          </el-select>
        </div>

        <div class="filter-item">
          <label>科目：</label>
          <el-select v-model="filters.subject" placeholder="选择科目" clearable @change="handleFilterChange">
            <el-option label="全部" value="" />
            <el-option v-for="subject in filterOptions.subjects" :key="subject" :label="subject" :value="subject" />
          </el-select>
        </div>

        <div class="filter-item">
          <label>册别：</label>
          <el-select v-model="filters.volume" placeholder="选择册别" clearable @change="handleFilterChange">
            <el-option label="全部" value="" />
            <el-option v-for="volume in filterOptions.volumes" :key="volume" :label="volume" :value="volume" />
          </el-select>
        </div>

        <div class="filter-item">
          <label>板块：</label>
          <el-select v-model="filters.section" placeholder="选择板块" clearable @change="handleFilterChange">
            <el-option label="全部" value="" />
            <el-option v-for="section in filterOptions.sections" :key="section" :label="section" :value="section" />
          </el-select>
        </div>

        <div class="filter-item">
          <label>状态：</label>
          <el-select v-model="filters.status" @change="handleFilterChange">
            <el-option label="活跃" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </div>

        <div class="filter-item">
          <label>创建时间：</label>
          <el-select v-model="filters.timeRange" placeholder="选择时间范围" clearable @change="handleTimeRangeChange">
            <el-option label="全部" value="" />
            <el-option label="今天" value="today" />
            <el-option label="昨天" value="yesterday" />
            <el-option label="最近7天" value="last7days" />
            <el-option label="最近30天" value="last30days" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </div>
      </div>

      <!-- 自定义时间范围选择器 -->
      <div class="filter-row" v-if="filters.timeRange === 'custom'">
        <div class="filter-item custom-date-item">
          <label>自定义时间：</label>
          <el-date-picker
            v-model="customDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleCustomDateChange"
            clearable
          />
        </div>
      </div>

      <div class="filter-row">
        <div class="filter-item search-item">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索文件标题、描述或标签..."
            @keyup.enter="handleFilterChange"
            @clear="handleFilterChange"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="filter-actions">
          <el-button @click="handleFilterChange">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button @click="batchDiagnose" type="warning" plain>
            <el-icon><Tools /></el-icon>
            批量诊断
          </el-button>
          <el-button @click="checkStorageConfig" type="info" plain>
            <el-icon><Setting /></el-icon>
            存储配置
          </el-button>

          <!-- 排序工具 -->
          <div class="sort-tools-inline">
            <span class="sort-label">排序:</span>
            <el-select v-model="sortConfig.field" @change="handleSortChange" style="width: 120px" size="small">
              <el-option label="上传时间" value="created_time" />
              <el-option label="权重" value="sort_order" />
              <el-option label="下载量" value="download_count" />
              <el-option label="阅读量" value="view_count" />
            </el-select>
            <el-button
              @click="toggleSortOrder"
              :type="sortConfig.order === 'desc' ? 'primary' : 'default'"
              size="small"
            >
              <el-icon v-if="sortConfig.order === 'desc'"><ArrowDown /></el-icon>
              <el-icon v-else><ArrowUp /></el-icon>
              {{ sortConfig.order === 'desc' ? '降序' : '升序' }}
            </el-button>
          </div>

          <!-- 每页显示数量 -->
          <div class="page-size-tools">
            <span class="page-size-label">每页:</span>
            <el-select v-model="pagination.pageSize" @change="handlePageSizeChange" style="width: 80px" size="small">
              <el-option label="10" :value="10" />
              <el-option label="20" :value="20" />
              <el-option label="50" :value="50" />
              <el-option label="100" :value="100" />
            </el-select>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 批量操作栏 -->
    <div v-if="selectedFiles.length > 0" class="batch-actions">
      <div class="batch-info">
        已选择 <strong>{{ selectedFiles.length }}</strong> 个文件
      </div>
      <div class="batch-buttons">
        <el-button type="success" @click="batchUpdateStatus('active')">
          <el-icon><Check /></el-icon>
          批量启用
        </el-button>
        <el-button type="warning" @click="batchUpdateStatus('inactive')">
          <el-icon><Close /></el-icon>
          批量禁用
        </el-button>
        <el-button type="danger" @click="batchDeleteFiles">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 快速统计 -->
    <el-card v-if="quickStats" class="quick-stats-card" shadow="never">
      <div class="quick-stats">
        <div class="stat-item">
          <div class="stat-value">{{ quickStats.total || 0 }}</div>
          <div class="stat-label">总文件数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ quickStats.activeCount || 0 }}</div>
          <div class="stat-label">活跃文件</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ quickStats.todayUploads || 0 }}</div>
          <div class="stat-label">今日上传</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ quickStats.totalDownloads || 0 }}</div>
          <div class="stat-label">总下载量</div>
        </div>
      </div>
    </el-card>

    <!-- 文件列表 -->
    <el-card class="table-card">
      <div class="table-header">
        <div class="table-title">
          <el-checkbox
            :model-value="selectAllStatus.checked"
            :indeterminate="selectAllStatus.indeterminate"
            @change="handleSelectAll"
            class="select-all-checkbox"
          />
          文件列表 (共 {{ pagination.total }} 个)
        </div>
      </div>

      <!-- 文件卡片列表 -->
      <div class="file-cards-container" v-loading="loading">
        <div
          v-for="row in fileList"
          :key="row._id"
          class="file-card-clean"
          :class="{ 'selected': selectedFiles.includes(row._id) }"
        >
          <!-- 卡片头部：选择框、标题、状态 -->
          <div class="card-header-clean">
            <div class="header-left">
              <el-checkbox
                :model-value="selectedFiles.includes(row._id)"
                @change="toggleFileSelection(row)"
                class="file-checkbox"
              />
              <div class="file-title-area">
                <!-- 标题输入框 -->
                <el-input
                  v-model="row.title"
                  placeholder="文件标题"
                  @blur="updateField(row._id, 'title', row.title)"
                  class="title-input-clean"
                  size="small"
                />

                <!-- 预览图区域 - 放在标题下方 -->
                <div class="preview-section" v-if="hasPreviewImages(row)">
                  <el-popover
                    placement="right"
                    :width="600"
                    trigger="hover"
                    @show="loadPreviewImages(row)"
                  >
                    <template #reference>
                      <div class="preview-trigger-wrapper">
                        <el-icon
                          class="preview-trigger"
                          :class="{ 'loading': row.previewLoading }"
                        >
                          <Loading v-if="row.previewLoading" class="is-loading" />
                          <View v-else />
                        </el-icon>
                        <span class="preview-label">预览图</span>
                        <span class="preview-count">{{ getPreviewCount(row) }}</span>
                      </div>
                    </template>
                    <div class="preview-popover-simple">
                      <div v-if="row.previewLoading" class="preview-loading">
                        <el-icon class="is-loading"><Loading /></el-icon>
                        <span>加载预览图...</span>
                      </div>
                      <div v-else-if="row.previewUrls && row.previewUrls.length > 0" class="preview-grid-simple">
                        <img
                          v-for="(img, index) in row.previewUrls"
                          :key="index"
                          :src="img"
                          :alt="`预览图 ${index + 1}`"
                          class="preview-img-simple"
                          @error="handleImageError"
                        />
                      </div>
                      <div v-else class="no-preview">
                        暂无预览图
                      </div>
                    </div>
                  </el-popover>
                </div>

                <!-- 文件元信息 -->
                <div class="file-meta-clean">
                  <span class="meta-item">{{ row.file_type?.toUpperCase() }}</span>
                  <span class="meta-item">{{ formatFileSize(row.file_size) }}</span>
                  <span class="meta-item" v-if="row.pages">{{ row.pages }}页</span>
                  <span class="meta-item">{{ formatDate(row.created_time) }}</span>
                </div>
              </div>
            </div>
            <div class="header-right">
              <div class="status-toggle">
                <span class="status-label">启用</span>
                <el-switch
                  v-model="row.status"
                  active-value="active"
                  inactive-value="inactive"
                  @change="updateField(row._id, 'status', row.status)"
                  size="small"
                />
              </div>
            </div>
          </div>

          <!-- 卡片主体：分类和数据 -->
          <div class="card-body-clean">
            <!-- 分类信息 -->
            <div class="category-row">
              <div class="category-group">
                <label>年级</label>
                <el-select v-model="row.grade" size="small" @change="updateField(row._id, 'grade', row.grade)" filterable allow-create>
                  <el-option label="幼升小" value="幼升小" />
                  <el-option label="一年级" value="一年级" />
                  <el-option label="二年级" value="二年级" />
                  <el-option label="三年级" value="三年级" />
                  <el-option label="四年级" value="四年级" />
                  <el-option label="五年级" value="五年级" />
                  <el-option label="六年级" value="六年级" />
                  <el-option label="小升初" value="小升初" />
                </el-select>
              </div>

              <div class="category-group">
                <label>科目</label>
                <el-select v-model="row.subject" size="small" @change="updateField(row._id, 'subject', row.subject)" filterable allow-create>
                  <el-option label="语文" value="语文" />
                  <el-option label="数学" value="数学" />
                  <el-option label="英语" value="英语" />
                  <el-option label="科学" value="科学" />
                  <el-option label="音乐" value="音乐" />
                  <el-option label="美术" value="美术" />
                  <el-option label="体育" value="体育" />
                </el-select>
              </div>

              <div class="category-group">
                <label>册别</label>
                <el-select v-model="row.volume" size="small" @change="updateField(row._id, 'volume', row.volume)" filterable allow-create>
                  <el-option label="上册" value="上册" />
                  <el-option label="下册" value="下册" />
                  <el-option label="全册" value="全册" />
                </el-select>
              </div>

              <div class="category-group">
                <label>板块</label>
                <el-select v-model="row.section" size="small" @change="updateField(row._id, 'section', row.section)" filterable allow-create>
                  <el-option label="单元同步" value="单元同步" />
                  <el-option label="单元知识点" value="单元知识点" />
                  <el-option label="核心知识点" value="核心知识点" />
                  <el-option label="试卷" value="试卷" />
                  <el-option label="专项练习" value="专项练习" />
                  <el-option label="拼音启蒙" value="拼音启蒙" />
                  <el-option label="认识数字" value="认识数字" />
                  <el-option label="习惯养成" value="习惯养成" />
                  <el-option label="学科启蒙" value="学科启蒙" />
                  <el-option label="知识科普" value="知识科普" />
                  <el-option label="语文冲刺" value="语文冲刺" />
                  <el-option label="数学冲刺" value="数学冲刺" />
                  <el-option label="英语强化" value="英语强化" />
                  <el-option label="真题模拟" value="真题模拟" />
                  <el-option label="面试准备" value="面试准备" />
                </el-select>
              </div>
            </div>

            <!-- 数据和操作行 -->
            <div class="data-actions-row">
              <!-- 数据组 -->
              <div class="data-group">
                <div class="data-item">
                  <label>下载</label>
                  <el-input v-model="row.download_count" size="small" @blur="updateStatField(row._id, 'download_count', parseInt(row.download_count) || 0)" />
                </div>
                <div class="data-item">
                  <label>查看</label>
                  <el-input v-model="row.view_count" size="small" @blur="updateStatField(row._id, 'view_count', parseInt(row.view_count) || 0)" />
                </div>
                <div class="data-item">
                  <label>权重</label>
                  <el-input v-model="row.sort_order" size="small" @blur="updateField(row._id, 'sort_order', parseInt(row.sort_order) || 0)" />
                </div>
                <div class="data-item">
                  <label>广告</label>
                  <el-input v-model="row.ad_required_count" size="small" @blur="updateField(row._id, 'ad_required_count', parseInt(row.ad_required_count) || 1)" />
                </div>
              </div>

              <!-- 操作组 -->
              <div class="actions-group">
                <el-button size="small" type="primary" @click="downloadFile(row)" :loading="row.downloading">
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
                <el-button size="small" type="info" @click="diagnoseFile(row)" plain>
                  <el-icon><Tools /></el-icon>
                  诊断
                </el-button>
                <el-button size="small" type="danger" @click="deleteFile(row)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>

          <!-- 描述和标签区域（可折叠） -->
          <div class="card-extra-clean" v-if="row.showExtra">
            <div class="desc-area-clean">
              <label>描述</label>
              <el-input
                v-model="row.description"
                type="textarea"
                :rows="2"
                placeholder="文件描述..."
                @blur="updateField(row._id, 'description', row.description)"
                size="small"
              />
            </div>
            <div class="tags-features-clean">
              <div class="tags-section-clean">
                <label>标签</label>
                <div class="tags-content-clean">
                  <el-tag v-for="(tag, index) in (row.tags || [])" :key="index" size="small" closable @close="removeTag(row, index)">{{ tag }}</el-tag>
                  <el-button v-if="!row.showTagInput" size="small" @click="showTagInput(row)">+标签</el-button>
                  <el-input v-if="row.showTagInput" v-model="row.newTag" size="small" @keyup.enter="addTag(row)" @blur="addTag(row)" placeholder="输入标签" style="width: 100px;" />
                </div>
              </div>
              <div class="features-section-clean">
                <label>特征</label>
                <div class="features-content-clean">
                  <el-tag v-for="(feature, index) in (row.features || [])" :key="index" type="success" size="small" closable @close="removeFeature(row, index)">{{ feature }}</el-tag>
                  <el-button v-if="!row.showFeatureInput" size="small" type="success" @click="showFeatureInput(row)">+特征</el-button>
                  <el-input v-if="row.showFeatureInput" v-model="row.newFeature" size="small" @keyup.enter="addFeature(row)" @blur="addFeature(row)" placeholder="输入特征" style="width: 100px;" />
                </div>
              </div>
            </div>
          </div>

          <!-- 展开/收起按钮 -->
          <div class="card-toggle-clean" @click="row.showExtra = !row.showExtra">
            <span>{{ row.showExtra ? '收起详情' : '展开详情' }}</span>
            <el-icon><ArrowDown v-if="!row.showExtra" /><ArrowUp v-else /></el-icon>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
          small
        />
      </div>
    </el-card>

    <!-- 文件上传对话框 -->
    <FileUploadDialog
      v-model="showUploadDialog"
      @success="handleUploadSuccess"
    />

    <!-- CSV批量上传对话框 -->
    <CsvUploadDialog
      v-model="showCsvUploadDialog"
      @success="handleUploadSuccess"
    />

    <!-- 文件编辑对话框 -->
    <FileEditDialog
      v-model="showEditDialog"
      :file-id="editingFileId"
      @success="handleEditSuccess"
    />

    <!-- 简化后不需要预览对话框了 -->
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Document, Download, Search, Refresh, Check, Close, Delete, View, Edit, Link, Picture, ArrowDown, ArrowUp, Loading, ZoomIn, Tools, Setting } from '@element-plus/icons-vue'
import { fileApi } from '@/api/files'
import FileUploadDialog from '@/components/FileUploadDialog.vue'
import CsvUploadDialog from '@/components/CsvUploadDialog.vue'
import FileEditDialog from '@/components/FileEditDialog.vue'

// 响应式数据
const loading = ref(false)
const fileList = ref([])
const selectedFiles = ref([])
const showUploadDialog = ref(false)
const showCsvUploadDialog = ref(false)
const showEditDialog = ref(false)
const editingFileId = ref('')
const quickStats = ref(null)

// 预览相关辅助方法
// 检查文件是否有预览图
const hasPreviewImages = (file) => {
  return file.preview_images && Array.isArray(file.preview_images) && file.preview_images.length > 0
}

// 获取预览图数量
const getPreviewCount = (file) => {
  if (!hasPreviewImages(file)) return 0
  return file.preview_images.length
}

// 筛选器
const filters = reactive({
  grade: '',
  subject: '',
  volume: '',
  section: '',
  status: 'active',
  keyword: '',
  timeRange: '',        // 新增：时间范围类型
  startDate: '',        // 新增：开始日期
  endDate: ''           // 新增：结束日期
})

// 自定义日期范围
const customDateRange = ref([])

// 排序配置
const sortConfig = reactive({
  field: 'created_time',  // 默认按上传时间排序
  order: 'desc'           // 默认降序，新上传的在前面
})

// 筛选选项
const filterOptions = reactive({
  grades: [],
  subjects: [],
  volumes: [],
  sections: [],
  categories: []
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 全选状态管理
const selectAllStatus = computed(() => {
  const currentPageFiles = fileList.value.map(file => file._id)
  const selectedInCurrentPage = selectedFiles.value.filter(id =>
    currentPageFiles.includes(id)
  )

  return {
    checked: selectedInCurrentPage.length === currentPageFiles.length && currentPageFiles.length > 0,
    indeterminate: selectedInCurrentPage.length > 0 && selectedInCurrentPage.length < currentPageFiles.length
  }
})

// 生命周期
onMounted(() => {
  loadFilterOptions()
  loadFileList()
  loadQuickStats()
})

// 方法
const loadFilterOptions = async () => {
  try {
    const result = await fileApi.getFilterOptions()
    if (result.success) {
      Object.assign(filterOptions, result.data)
    }
  } catch (error) {
    console.error('加载筛选选项失败:', error)
  }
}

const loadFileList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...filters,
      // 添加排序参数
      sortBy: sortConfig.field,
      sortOrder: sortConfig.order
    }

    const result = await fileApi.getFileList(params)
    if (result.success) {
      // 初始化响应式属性，确保所有字段都有默认值
      fileList.value = (result.data.files || result.data.list || []).map(file => ({
        ...file,
        // 基础字段默认值
        title: file.title || '',
        description: file.description || '',
        grade: file.grade || '',
        subject: file.subject || '',
        volume: file.volume || '',
        section: file.section || '',
        status: file.status || 'active',
        // 数字字段默认值
        download_count: file.download_count || 0,
        view_count: file.view_count || 0,
        sort_order: file.sort_order || 0,
        ad_required_count: file.ad_required_count || 1,
        pages: file.pages || 0,
        // 数组字段默认值
        tags: Array.isArray(file.tags) ? file.tags : [],
        features: Array.isArray(file.features) ? file.features : [],
        preview_images: Array.isArray(file.preview_images) ? file.preview_images : [],
        // 预览图相关状态
        previewLoading: false,
        previewLoaded: false,
        previewUrls: [], // 存储转换后的预览图URL
        // 其他响应式状态
        downloading: false,
        showTagInput: false,
        newTag: '',
        showFeatureInput: false,
        newFeature: ''
      }))
      pagination.total = result.data.total
    }
  } catch (error) {
    ElMessage.error('加载文件列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleFilterChange = () => {
  pagination.page = 1
  loadFileList()
}

const resetFilters = () => {
  Object.assign(filters, {
    grade: '',
    subject: '',
    volume: '',
    section: '',
    status: 'active',
    keyword: '',
    timeRange: '',
    startDate: '',
    endDate: ''
  })
  // 重置自定义日期范围
  customDateRange.value = []
  // 重置排序为默认值
  Object.assign(sortConfig, {
    field: 'created_time',
    order: 'desc'
  })
  handleFilterChange()
}

// 处理全选/取消全选
const handleSelectAll = (checked) => {
  const currentPageFileIds = fileList.value.map(file => file._id)

  if (checked) {
    // 全选：添加当前页所有文件ID到选中列表
    const newSelected = [...new Set([...selectedFiles.value, ...currentPageFileIds])]
    selectedFiles.value = newSelected
  } else {
    // 取消全选：从选中列表中移除当前页所有文件ID
    selectedFiles.value = selectedFiles.value.filter(id => !currentPageFileIds.includes(id))
  }
}

// 日期格式化工具函数（用于时间筛选）
const formatDateForFilter = (date) => {
  return date.toISOString().split('T')[0]
}

// 处理时间范围变化
const handleTimeRangeChange = (value) => {
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  switch (value) {
    case 'today':
      filters.startDate = formatDateForFilter(today)
      filters.endDate = formatDateForFilter(today)
      break
    case 'yesterday':
      filters.startDate = formatDateForFilter(yesterday)
      filters.endDate = formatDateForFilter(yesterday)
      break
    case 'last7days':
      const last7days = new Date(today)
      last7days.setDate(last7days.getDate() - 7)
      filters.startDate = formatDateForFilter(last7days)
      filters.endDate = formatDateForFilter(today)
      break
    case 'last30days':
      const last30days = new Date(today)
      last30days.setDate(last30days.getDate() - 30)
      filters.startDate = formatDateForFilter(last30days)
      filters.endDate = formatDateForFilter(today)
      break
    case 'custom':
      // 自定义范围，等待用户选择
      break
    default:
      filters.startDate = ''
      filters.endDate = ''
      customDateRange.value = []
  }

  if (value !== 'custom') {
    handleFilterChange()
  }
}

// 处理自定义日期范围变化
const handleCustomDateChange = (dateRange) => {
  if (dateRange && dateRange.length === 2) {
    filters.startDate = dateRange[0]
    filters.endDate = dateRange[1]
    handleFilterChange()
  } else if (!dateRange) {
    // 清空自定义日期范围
    filters.startDate = ''
    filters.endDate = ''
    handleFilterChange()
  }
}

// 排序处理
const handleSortChange = () => {
  pagination.page = 1
  loadFileList()
}

const toggleSortOrder = () => {
  sortConfig.order = sortConfig.order === 'desc' ? 'asc' : 'desc'
  handleSortChange()
}

// 每页显示数量变更（已在下面定义，删除重复）

const handleSelectionChange = (selection) => {
  selectedFiles.value = selection
}

// 切换文件选择状态
const toggleFileSelection = (file) => {
  const index = selectedFiles.value.indexOf(file._id)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else {
    selectedFiles.value.push(file._id)
  }
}

const handlePageChange = (page) => {
  pagination.page = page
  loadFileList()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadFileList()
}

// 更新统计字段（下载量、查看量）
const updateStatField = async (fileId, field, value) => {
  try {
    // 数字验证
    const numValue = parseInt(value) || 0
    if (numValue < 0) {
      ElMessage.error('数值不能为负数')
      loadFileList() // 恢复原值
      return
    }

    // 找到当前文件数据
    const currentFile = fileList.value.find(file => file._id === fileId)
    if (!currentFile) {
      ElMessage.error('文件不存在')
      return
    }

    // 构建更新数据，包含所有必需字段
    const updateData = {
      title: currentFile.title || '',
      description: currentFile.description || '',
      grade: currentFile.grade || '',
      subject: currentFile.subject || '',
      volume: currentFile.volume || '',
      section: currentFile.section || '',
      category: (currentFile.category && ['regular', 'upgrade'].includes(currentFile.category)) ? currentFile.category : 'regular',
      tags: currentFile.tags || [],
      ad_required_count: currentFile.ad_required_count || 1,
      sort_order: currentFile.sort_order || 0,
      features: currentFile.features || [],
      status: currentFile.status || 'active',
      // 更新统计字段
      download_count: field === 'download_count' ? numValue : (currentFile.download_count || 0),
      view_count: field === 'view_count' ? numValue : (currentFile.view_count || 0)
    }

    console.log(`更新统计字段 ${field}:`, { fileId, field, value: numValue, updateData })
    const result = await fileApi.updateFile(fileId, updateData)
    console.log(`更新统计字段结果:`, result)

    if (result.success) {
      ElMessage.success(`${field === 'download_count' ? '下载量' : '阅读量'}更新成功`)
    } else {
      ElMessage.error(`更新失败: ${result.error}`)
      loadFileList() // 恢复原值
    }
  } catch (error) {
    ElMessage.error(`更新失败: ${error.message}`)
    loadFileList() // 恢复原值
  }
}

// 动态更新字段
const updateField = async (fileId, field, value) => {
  try {
    // 字段验证
    const validation = validateField(field, value)
    if (!validation.valid) {
      ElMessage.error(validation.message)
      // 刷新列表以恢复原值
      loadFileList()
      return
    }

    // 找到当前文件数据
    const currentFile = fileList.value.find(file => file._id === fileId)
    if (!currentFile) {
      ElMessage.error('文件不存在')
      return
    }

    // 构建更新数据，只包含后端允许的字段
    const updateData = {
      title: currentFile.title || '',
      description: currentFile.description || '',
      grade: currentFile.grade || '',
      subject: currentFile.subject || '',
      volume: currentFile.volume || '',
      section: currentFile.section || '',
      category: (currentFile.category && ['regular', 'upgrade'].includes(currentFile.category)) ? currentFile.category : 'regular',
      tags: currentFile.tags || [],
      ad_required_count: currentFile.ad_required_count || 1,
      sort_order: currentFile.sort_order || 0,
      features: currentFile.features || [],
      status: currentFile.status || 'active',
      // 更新指定字段
      [field]: value
    }

    const result = await fileApi.updateFile(fileId, updateData)
    if (result.success) {
      ElMessage.success(`${getFieldName(field)}更新成功`)
    } else {
      ElMessage.error(`更新失败: ${result.error}`)
      // 刷新列表以恢复原值
      loadFileList()
    }
  } catch (error) {
    ElMessage.error(`更新失败: ${error.message}`)
    // 刷新列表以恢复原值
    loadFileList()
  }
}

// 字段验证
const validateField = (field, value) => {
  switch (field) {
    case 'title':
      if (!value || !value.trim()) {
        return { valid: false, message: '标题不能为空' }
      }
      if (value.trim().length > 100) {
        return { valid: false, message: '标题长度不能超过100个字符' }
      }
      break
    case 'description':
      if (value && value.length > 500) {
        return { valid: false, message: '描述长度不能超过500个字符' }
      }
      break
    case 'subject':
      if (!value || !value.trim()) {
        return { valid: false, message: '科目不能为空' }
      }
      break
    case 'volume':
      if (!value || !value.trim()) {
        return { valid: false, message: '册别不能为空' }
      }
      break
    case 'section':
      if (!value || !value.trim()) {
        return { valid: false, message: '板块不能为空' }
      }
      break
    case 'download_count':
    case 'view_count':
    case 'sort_order':
      if (value < 0) {
        return { valid: false, message: `${getFieldName(field)}不能为负数` }
      }
      break
    case 'ad_required_count':
      if (value < 1 || value > 10) {
        return { valid: false, message: '广告次数必须在1-10之间' }
      }
      break
  }
  return { valid: true }
}

// 获取字段中文名称
const getFieldName = (field) => {
  const fieldNames = {
    title: '标题',
    description: '描述',
    grade: '年级',
    subject: '科目',
    volume: '册别',
    section: '板块',
    download_count: '下载量',
    view_count: '查看量',
    sort_order: '权重',
    ad_required_count: '广告次数',
    status: '状态',
    tags: '标签',
    features: '特征'
  }
  return fieldNames[field] || field
}

// 标签管理
const showTagInput = (row) => {
  row.showTagInput = true
  row.newTag = ''
  // 下一帧聚焦输入框
  setTimeout(() => {
    const inputs = document.querySelectorAll('.tag-input input')
    const targetInput = inputs[inputs.length - 1]
    if (targetInput) targetInput.focus()
  }, 100)
}

const addTag = async (row) => {
  if (!row.newTag || !row.newTag.trim()) {
    row.showTagInput = false
    return
  }

  const newTag = row.newTag.trim()
  if (!row.tags) row.tags = []

  if (!row.tags.includes(newTag)) {
    row.tags.push(newTag)
    await updateField(row._id, 'tags', row.tags)
  }

  row.newTag = ''
  row.showTagInput = false
}

const removeTag = async (row, index) => {
  if (!row.tags) return
  row.tags.splice(index, 1)
  await updateField(row._id, 'tags', row.tags)
}

// 特征管理
const showFeatureInput = (row) => {
  row.showFeatureInput = true
  row.newFeature = ''
  // 下一帧聚焦输入框
  setTimeout(() => {
    const inputs = document.querySelectorAll('.feature-input input')
    const targetInput = inputs[inputs.length - 1]
    if (targetInput) targetInput.focus()
  }, 100)
}

const addFeature = async (row) => {
  if (!row.newFeature || !row.newFeature.trim()) {
    row.showFeatureInput = false
    return
  }

  const newFeature = row.newFeature.trim()
  if (!row.features) row.features = []

  if (!row.features.includes(newFeature)) {
    row.features.push(newFeature)
    await updateField(row._id, 'features', row.features)
  }

  row.newFeature = ''
  row.showFeatureInput = false
}

const removeFeature = async (row, index) => {
  if (!row.features) return
  row.features.splice(index, 1)
  await updateField(row._id, 'features', row.features)
}

// 文件地址和预览图操作
const viewFileUrl = (file) => {
  if (file.file_url) {
    // 显示文件地址对话框
    ElMessageBox.alert(file.file_url, '文件存储地址', {
      confirmButtonText: '复制地址',
      callback: () => {
        navigator.clipboard.writeText(file.file_url).then(() => {
          ElMessage.success('文件地址已复制到剪贴板')
        }).catch(() => {
          ElMessage.error('复制失败，请手动复制')
        })
      }
    })
  } else {
    ElMessage.warning('该文件暂无存储地址')
  }
}

const viewPreviewImages = (file) => {
  if (file.preview_images && file.preview_images.length > 0) {
    // 显示预览图地址列表
    const imageUrls = file.preview_images.map((img, index) => `第${index + 1}张: ${img.url || img}`).join('\n')
    ElMessageBox.alert(imageUrls, '预览图地址', {
      confirmButtonText: '关闭'
    })
  } else {
    ElMessage.warning('该文件暂无预览图')
  }
}

const reuploadFile = (file) => {
  ElMessageBox.confirm(
    `确定要重新上传文件 "${file.title}" 吗？这将替换现有文件。`,
    '重新上传文件',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // TODO: 实现文件重新上传功能
    ElMessage.info('文件重新上传功能开发中...')
  }).catch(() => {
    // 用户取消
  })
}

const reuploadPreview = (file) => {
  ElMessageBox.confirm(
    `确定要重新生成文件 "${file.title}" 的预览图吗？`,
    '重新生成预览图',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // TODO: 实现预览图重新生成功能
    ElMessage.info('预览图重新生成功能开发中...')
  }).catch(() => {
    // 用户取消
  })
}

// 文件操作
const viewFile = (file) => {
  // TODO: 实现文件查看功能
  console.log('查看文件:', file)
}

const editFile = (file) => {
  editingFileId.value = file._id
  showEditDialog.value = true
}

// downloadFile 函数已在下面重新定义，删除旧版本

const deleteFile = async (file) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${file.title}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await fileApi.deleteFile(file._id)
    if (result.success) {
      // 显示详细的删除结果
      if (result.message) {
        ElMessage.success(result.message)
      } else {
        ElMessage.success('删除成功')
      }

      // 如果有跳过的文件，显示提示信息
      if (result.skippedFiles && result.skippedFiles.length > 0) {
        console.log('跳过删除的文件（被其他记录引用）:', result.skippedFiles)
        ElMessage.info(`有 ${result.skippedFiles.length} 个云存储文件因被其他记录引用而保留`)
      }

      loadFileList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 批量操作
const batchUpdateStatus = async (status) => {
  try {
    // selectedFiles.value 已经是文件ID字符串数组，不需要再map
    const fileIds = selectedFiles.value
    const result = await fileApi.batchUpdateStatus(fileIds, status)
    if (result.success) {
      ElMessage.success(`批量${status === 'active' ? '启用' : '禁用'}成功`)
      selectedFiles.value = []
      loadFileList()
      loadQuickStats() // 更新统计数据
    } else {
      ElMessage.error('批量操作失败: ' + result.error)
    }
  } catch (error) {
    ElMessage.error('批量操作失败: ' + error.message)
  }
}

const batchDeleteFiles = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // selectedFiles.value 已经是文件ID字符串数组，不需要再map
    const fileIds = selectedFiles.value
    const result = await fileApi.batchDeleteFiles(fileIds)
    if (result.success) {
      ElMessage.success('批量删除成功')
      selectedFiles.value = []
      loadFileList()
      loadQuickStats() // 更新统计数据
    } else {
      ElMessage.error('批量删除失败: ' + result.error)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败: ' + error.message)
    }
  }
}

// CSV相关
const downloadCsvTemplate = async () => {
  try {
    const result = await fileApi.downloadCsvTemplate()

    // 创建下载链接
    const blob = new Blob([result], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'files_upload_template.csv'
    link.click()
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
  } catch (error) {
    ElMessage.error('下载模板失败: ' + error.message)
  }
}

const loadQuickStats = async () => {
  try {
    // 获取所有文件的统计数据（不受筛选影响）
    // 分别获取活跃和非活跃文件进行统计
    const [activeResult, inactiveResult] = await Promise.all([
      fileApi.getFileList({
        page: 1,
        pageSize: 100,
        status: 'active'
      }),
      fileApi.getFileList({
        page: 1,
        pageSize: 100,
        status: 'inactive'
      })
    ])

    // 合并结果
    const allFiles = [
      ...(activeResult.success ? (activeResult.data.files || activeResult.data.list || []) : []),
      ...(inactiveResult.success ? (inactiveResult.data.files || inactiveResult.data.list || []) : [])
    ]

    // 计算统计数据
    const activeFiles = allFiles.filter(file => file.status === 'active')

    // 计算今日上传数
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayUploads = allFiles.filter(file => {
      const fileDate = new Date(file.created_time)
      fileDate.setHours(0, 0, 0, 0)
      return fileDate.getTime() === today.getTime()
    }).length

    // 计算总下载量
    const totalDownloads = allFiles.reduce((sum, file) => sum + (file.download_count || 0), 0)

    quickStats.value = {
      total: allFiles.length, // 总文件数（包括活跃和禁用）
      activeCount: activeFiles.length, // 只统计活跃文件
      todayUploads: todayUploads, // 今日上传数
      totalDownloads: totalDownloads // 总下载量
    }
  } catch (error) {
    console.error('加载快速统计失败:', error)
  }
}

const handleUploadSuccess = () => {
  loadFileList()
  loadQuickStats()
}

const handleEditSuccess = () => {
  loadFileList()
  loadQuickStats()
}

// 下载文件
const downloadFile = async (file) => {
  try {
    file.downloading = true

    // 获取下载链接
    console.log('请求下载链接:', file._id, file.title)
    const result = await fileApi.getDownloadUrl(file._id)
    console.log('下载链接响应:', result)

    if (result.success && result.data && result.data.downloadUrl) {
      const downloadUrl = result.data.downloadUrl

      // 验证URL格式
      if (!downloadUrl.startsWith('http://') && !downloadUrl.startsWith('https://')) {
        console.error('下载链接格式错误:', downloadUrl)
        ElMessage.error('下载链接格式错误，请联系管理员')
        return
      }

      console.log('打开下载链接:', downloadUrl)
      // 直接在新窗口打开下载链接
      window.open(downloadUrl, '_blank')

      // 更新下载次数（乐观更新）
      const newCount = (file.download_count || 0) + 1
      file.download_count = newCount

      // 异步更新到服务器
      try {
        await updateStatField(file._id, 'download_count', newCount)
      } catch (updateError) {
        console.warn('更新下载次数失败:', updateError)
        // 不影响下载功能
      }

      // 显示成功或警告信息
      if (result.warning) {
        ElMessage.warning(result.warning)
      } else {
        ElMessage.success('文件下载已开始')
      }
    } else {
      const errorMsg = result.error || '获取下载链接失败'
      console.error('下载链接获取失败:', errorMsg, result)
      ElMessage.error(errorMsg)
    }
  } catch (error) {
    console.error('下载请求失败:', error)
    ElMessage.error('下载失败: ' + (error.response?.data?.error || error.message))
  } finally {
    file.downloading = false
  }
}

// 加载预览图（只在悬停时调用）
const loadPreviewImages = async (file) => {
  try {
    // 如果正在加载，避免重复请求
    if (file.previewLoading) {
      return
    }

    // 如果已经加载过预览图URL，直接使用
    if (file.previewUrls && file.previewUrls.length > 0) {
      console.log(`文件 ${file._id} 使用缓存的预览图:`, file.previewUrls.length, '张')
      return
    }

    // 如果没有预览图数据，直接返回
    if (!hasPreviewImages(file)) {
      file.previewLoaded = true
      return
    }

    file.previewLoading = true

    // 从API获取转换后的预览图URL
    console.log('悬停加载预览图:', file._id)
    const result = await fileApi.getFileDetail(file._id)
    console.log('预览图详情响应:', result)

    if (result.success && result.data) {
      if (result.data.preview_images && result.data.preview_images.length > 0) {
        // 存储转换后的URL到单独字段
        file.previewUrls = result.data.preview_images
        file.previewLoaded = true
        console.log('预览图加载成功:', file.previewUrls.length, '张')
      } else {
        // 如果没有预览图，标记为已加载避免重复请求
        file.previewUrls = []
        file.previewLoaded = true
        console.log('文件无可用预览图')
      }
    } else {
      console.error('获取文件详情失败:', result.error || '未知错误')
      file.previewUrls = []
      file.previewLoaded = true
    }
  } catch (error) {
    console.error('加载预览图请求失败:', error)
    file.previewUrls = []
    file.previewLoaded = true
  } finally {
    file.previewLoading = false
  }
}

// 诊断文件URL问题
const diagnoseFile = async (file) => {
  try {
    console.log('诊断文件:', file._id, file.title)
    const result = await fileApi.diagnoseFile(file._id)

    if (result.success) {
      const diagnosis = result.data
      console.log('文件诊断结果:', diagnosis)

      // 构建诊断信息
      let message = `文件诊断结果：\n\n`
      message += `文件ID: ${diagnosis.fileId}\n`
      message += `标题: ${diagnosis.title}\n`
      message += `文件URL: ${diagnosis.file_url}\n`
      message += `URL格式: ${diagnosis.url_format}\n`
      message += `URL长度: ${diagnosis.url_length}\n`
      message += `文件大小: ${diagnosis.file_size} 字节\n`
      message += `文件类型: ${diagnosis.file_type}\n`
      message += `创建时间: ${diagnosis.created_time}\n`

      // 添加临时URL测试结果
      if (diagnosis.temp_url_test) {
        message += `\n临时URL测试:\n`
        message += `测试结果: ${diagnosis.temp_url_test.success ? '✅ 成功' : '❌ 失败'}\n`
        message += `返回码: ${diagnosis.temp_url_test.code}\n`
        if (diagnosis.temp_url_test.message) {
          message += `消息: ${diagnosis.temp_url_test.message}\n`
        }
        if (diagnosis.temp_url_test.error) {
          message += `错误: ${diagnosis.temp_url_test.error}\n`
        }
        if (diagnosis.temp_url_sample) {
          message += `示例链接: ${diagnosis.temp_url_sample}\n`
        }
      }

      // 分析问题
      let issues = []
      if (!diagnosis.file_url) {
        issues.push('❌ 文件URL为空')
      } else if (diagnosis.url_format !== 'cloud://') {
        issues.push('❌ 文件URL格式不正确，应该以 cloud:// 开头')
        issues.push('💡 建议：检查文件上传过程是否正常')
      } else {
        issues.push('✅ 文件URL格式正确')

        // 检查URL结构
        const urlMatch = diagnosis.file_url.match(/^cloud:\/\/([^.]+)\.([^\/]+)(.*)$/)
        if (urlMatch) {
          const [, envPrefix, domain, path] = urlMatch
          issues.push(`📋 环境前缀: ${envPrefix}`)
          issues.push(`📋 域名: ${domain}`)
          issues.push(`📋 文件路径: ${path}`)
          issues.push(`🔗 预期HTTPS链接: https://${domain}.tcb.qcloud.la${path}`)
        } else {
          issues.push('⚠️ 无法解析URL结构，可能存在格式问题')
        }
      }

      if (diagnosis.url_length < 50) {
        issues.push('⚠️ 文件URL长度可能过短')
      }

      // 添加临时URL测试结果分析
      if (diagnosis.temp_url_test) {
        if (diagnosis.temp_url_test.success) {
          issues.push('✅ 临时URL获取成功，文件可以正常下载')
        } else {
          issues.push(`❌ 临时URL获取失败: ${diagnosis.temp_url_test.code}`)

          // 根据错误码提供具体建议
          switch (diagnosis.temp_url_test.code) {
            case 'STORAGE_FILE_NONEXIST':
              issues.push('💡 建议: 文件在云存储中不存在，检查文件上传是否成功')
              break
            case 'INVALID_PARAM':
              issues.push('💡 建议: 文件ID格式错误，检查数据库中的file_url字段')
              break
            default:
              issues.push('💡 建议: 检查云开发环境配置和权限设置')
          }
        }
      }

      if (issues.length > 0) {
        message += `\n问题分析:\n${issues.join('\n')}`
      }

      ElMessageBox.alert(message, '文件诊断结果', {
        confirmButtonText: '确定',
        type: issues.some(issue => issue.includes('❌')) ? 'error' : 'success'
      })
    } else {
      ElMessage.error('诊断失败: ' + result.error)
    }
  } catch (error) {
    console.error('诊断文件失败:', error)
    ElMessage.error('诊断失败: ' + error.message)
  }
}

// 批量诊断文件
const batchDiagnose = async () => {
  try {
    const files = fileList.value.slice(0, 10) // 只诊断前10个文件，避免请求过多

    if (files.length === 0) {
      ElMessage.warning('没有文件可以诊断')
      return
    }

    ElMessage.info(`开始诊断 ${files.length} 个文件...`)

    const results = []
    let successCount = 0
    let errorCount = 0

    for (const file of files) {
      try {
        const result = await fileApi.diagnoseFile(file._id)
        if (result.success) {
          const diagnosis = result.data
          let status = '✅ 正常'

          if (!diagnosis.file_url) {
            status = '❌ URL为空'
            errorCount++
          } else if (diagnosis.url_format !== 'cloud://') {
            status = '❌ URL格式错误'
            errorCount++
          } else {
            successCount++
          }

          results.push({
            title: diagnosis.title,
            status: status,
            url: diagnosis.file_url || '无',
            format: diagnosis.url_format
          })
        } else {
          results.push({
            title: file.title,
            status: '❌ 诊断失败',
            url: '无法获取',
            format: '未知'
          })
          errorCount++
        }
      } catch (error) {
        results.push({
          title: file.title,
          status: '❌ 请求失败',
          url: '无法获取',
          format: '未知'
        })
        errorCount++
      }
    }

    // 生成报告
    let report = `批量诊断完成！\n\n`
    report += `总计: ${files.length} 个文件\n`
    report += `正常: ${successCount} 个\n`
    report += `异常: ${errorCount} 个\n\n`
    report += `详细结果:\n`

    results.forEach((result, index) => {
      report += `${index + 1}. ${result.title}\n`
      report += `   状态: ${result.status}\n`
      report += `   格式: ${result.format}\n\n`
    })

    ElMessageBox.alert(report, '批量诊断结果', {
      confirmButtonText: '确定',
      type: errorCount > 0 ? 'warning' : 'success'
    })

  } catch (error) {
    console.error('批量诊断失败:', error)
    ElMessage.error('批量诊断失败: ' + error.message)
  }
}

// 检查云存储配置
const checkStorageConfig = async () => {
  try {
    const result = await fetch('/api/files/storage/config')
    const data = await result.json()

    if (data.success) {
      const config = data.data
      let message = `云存储配置信息：\n\n`
      message += `环境: ${config.environment}\n`
      message += `存储类型: ${config.storage_type}\n`
      message += `默认权限: ${config.default_permission}\n`
      message += `访问方式: ${config.access_method}\n\n`

      message += `重要说明:\n`
      config.notes.forEach((note, index) => {
        message += `${index + 1}. ${note}\n`
      })

      message += `\n故障排除:\n`
      config.troubleshooting.forEach((tip, index) => {
        message += `${index + 1}. ${tip}\n`
      })

      ElMessageBox.alert(message, '云存储配置', {
        confirmButtonText: '确定',
        type: 'info'
      })
    } else {
      ElMessage.error('获取配置失败: ' + data.error)
    }
  } catch (error) {
    console.error('检查存储配置失败:', error)
    ElMessage.error('检查存储配置失败: ' + error.message)
  }
}

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.src = '/placeholder-image.png' // 设置默认占位图
}

// 工具函数
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

// 标签颜色函数
const getSubjectTagType = (subject) => {
  const colorMap = {
    '语文': 'danger',
    '数学': 'primary',
    '英语': 'success',
    '科学': 'warning',
    '物理': 'info',
    '化学': 'danger',
    '生物': 'success'
  }
  return colorMap[subject] || ''
}

const getVolumeTagType = (volume) => {
  const colorMap = {
    '上册': 'primary',
    '下册': 'success',
    '全册': 'warning'
  }
  return colorMap[volume] || ''
}

const getSectionTagType = (section) => {
  const colorMap = {
    '单元同步': 'primary',
    '单元知识点': 'success',
    '核心知识点': 'warning',
    '期中期末试卷': 'danger',
    '专项练习': 'info',
    '试卷': 'danger'
  }
  return colorMap[section] || ''
}
</script>

<style scoped>
.file-management {
  padding: 24px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-card :deep(.el-card__body) {
  padding: 24px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 160px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  min-width: 40px;
  font-weight: 500;
}

/* 自定义时间范围样式 */
.custom-date-item {
  min-width: 300px;
}

.custom-date-item .el-date-picker {
  width: 280px;
}

.search-item {
  flex: 1;
}

.filter-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 内联排序工具样式 */
.sort-tools-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
  padding-left: 16px;
  border-left: 1px solid #e4e7ed;
}

.sort-label {
  font-size: 12px;
  color: #606266;
  font-weight: 600;
  white-space: nowrap;
}

/* 每页显示数量工具样式 */
.page-size-tools {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
  padding-left: 16px;
  border-left: 1px solid #e4e7ed;
}

.page-size-label {
  font-size: 12px;
  color: #606266;
  font-weight: 600;
  white-space: nowrap;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #409eff;
}

.batch-info {
  color: #606266;
}

.batch-buttons {
  display: flex;
  gap: 12px;
}

.quick-stats-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quick-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 20px 0;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-card :deep(.el-card__body) {
  padding: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.table-header .table-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

/* 全选复选框样式 */
.select-all-checkbox {
  margin-right: 8px;
}

.select-all-checkbox :deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
  background-color: #409eff;
  border-color: #409eff;
}

.table-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.file-info {
  padding: 8px 0;
}

.file-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 6px;
  font-size: 14px;
  line-height: 1.4;
}

.file-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 6px;
  flex-wrap: wrap;
}

.file-type,
.file-size,
.file-pages {
  font-size: 11px;
  color: #909399;
  background: #f5f7fa;
  padding: 3px 8px;
  border-radius: 3px;
  white-space: nowrap;
}

.file-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  max-height: 32px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.category-info {
  font-size: 13px;
  line-height: 1.8;
}

.category-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.category-row:last-child {
  margin-bottom: 0;
}

.category-label {
  color: #909399;
  min-width: 40px;
  font-size: 12px;
}

.category-value {
  color: #606266;
  font-weight: 500;
  font-size: 13px;
}

.stats-info {
  font-size: 13px;
  line-height: 1.8;
  color: #606266;
}

.stats-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.stats-row:last-child {
  margin-bottom: 0;
}

.stats-label {
  color: #909399;
  font-size: 12px;
}

.stats-value {
  color: #409eff;
  font-weight: 600;
  font-size: 13px;
}

.time-info {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: center;
}

.empty-value {
  color: #c0c4cc;
  font-style: italic;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-weight: 600;
  color: #409eff;
  font-size: 14px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 表格样式优化 */
.table-card :deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

.table-card :deep(.el-table__header) {
  background: #fafafa;
}

.table-card :deep(.el-table th) {
  background: #fafafa !important;
  color: #606266;
  font-weight: 600;
  border-bottom: 2px solid #ebeef5;
}

.table-card :deep(.el-table td) {
  border-bottom: 1px solid #f5f7fa;
}

.table-card :deep(.el-table__row:hover) {
  background: #f8f9fa !important;
}

/* 简洁卡片布局样式 */
.file-cards-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px 0;
}

.file-card-simple {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.file-card-simple:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 卡片主要内容 */
.card-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 16px;
}

/* 左侧：选择框和标题 */
.card-left-simple {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 2;
  min-width: 0;
}

.title-section-simple {
  flex: 1;
  min-width: 0;
}

.title-input-simple {
  margin-bottom: 4px;
}

.file-info-simple {
  display: flex;
  gap: 8px;
  font-size: 11px;
  color: #909399;
}

.file-info-simple span {
  background: #f5f7fa;
  padding: 1px 4px;
  border-radius: 2px;
  white-space: nowrap;
}

/* 中间：分类选择 */
.card-middle-simple {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.select-row {
  display: flex;
  gap: 8px;
}

.select-row .el-select {
  flex: 1;
}

.number-row {
  display: flex;
  gap: 8px;
}

.number-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 4px;
}

.number-item span {
  font-size: 11px;
  color: #606266;
  white-space: nowrap;
  min-width: 28px;
}

.number-item .el-input {
  flex: 1;
}

/* 右侧：状态和操作 */
.card-right-simple {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.status-section {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-section span {
  font-size: 11px;
  color: #606266;
  white-space: nowrap;
}

.actions-simple {
  display: flex;
  gap: 4px;
}

/* 展开区域 */
.card-extra {
  border-top: 1px solid #f0f2f5;
  padding: 12px 16px;
  background: #fafbfc;
}

.desc-area {
  margin-bottom: 12px;
}

.tags-area {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tags-section,
.features-section {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.tags-section span,
.features-section span {
  font-size: 11px;
  color: #606266;
  font-weight: 600;
  min-width: 32px;
}

.tags-section .el-tag,
.features-section .el-tag {
  font-size: 11px;
  height: 20px;
  line-height: 18px;
}

.tags-section .el-button,
.features-section .el-button {
  height: 20px;
  padding: 0 6px;
  font-size: 10px;
}

/* 展开/收起按钮 */
.card-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 6px;
  background: #f8f9fa;
  border-top: 1px solid #f0f2f5;
  cursor: pointer;
  font-size: 11px;
  color: #909399;
  transition: all 0.2s ease;
}

.card-toggle:hover {
  background: #f0f2f5;
  color: #606266;
}

/* 中间：分类信息 */
.category-section {
  margin-bottom: 16px;
}

.category-item {
  margin-bottom: 12px;
}

.category-item label {
  display: block;
  font-size: 12px;
  color: #606266;
  font-weight: 600;
  margin-bottom: 4px;
}

.card-select {
  width: 100%;
}

/* 统计数据 */
.stats-section {
  border-top: 1px solid #f0f2f5;
  padding-top: 16px;
}

.stat-item {
  margin-bottom: 12px;
}

.stat-item label {
  display: block;
  font-size: 12px;
  color: #606266;
  font-weight: 600;
  margin-bottom: 4px;
}

.card-number-input {
  width: 100%;
}

/* 右侧：操作区域 */
.actions-section {
  height: 100%;
}

.file-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-group label {
  font-size: 12px;
  color: #606266;
  font-weight: 600;
  margin-bottom: 4px;
}

.action-btn {
  width: 100%;
  justify-content: flex-start;
}

.file-title-input-enhanced {
  font-weight: 500;
}

.file-title-input-enhanced :deep(.el-input__inner) {
  font-weight: 500;
  border: 1px solid #dcdfe6;
  background: #fff;
  padding: 4px 8px;
  font-size: 14px;
  height: 32px;
}

.file-title-input-enhanced :deep(.el-input__inner):hover {
  border-color: #c0c4cc;
}

.file-title-input-enhanced :deep(.el-input__inner):focus {
  border-color: #409eff;
}

.file-meta-enhanced {
  display: flex;
  gap: 6px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.file-meta-enhanced .file-type,
.file-meta-enhanced .file-size,
.file-meta-enhanced .file-pages,
.file-meta-enhanced .ad-count {
  font-size: 11px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  white-space: nowrap;
}

.desc-section {
  margin-bottom: 8px;
}

.desc-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.file-desc-input-enhanced :deep(.el-textarea__inner) {
  border: 1px solid #dcdfe6;
  background: #fff;
  padding: 6px 8px;
  font-size: 12px;
  color: #606266;
  resize: none;
  min-height: 48px;
}

.file-desc-input-enhanced :deep(.el-textarea__inner):hover {
  border-color: #c0c4cc;
}

.file-desc-input-enhanced :deep(.el-textarea__inner):focus {
  border-color: #409eff;
  color: #303133;
}

.file-desc-input-enhanced :deep(.el-textarea__inner)::placeholder {
  color: #c0c4cc;
  font-style: italic;
}

.tags-features-enhanced {
  margin-top: 8px;
}

.tags-row,
.features-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 6px;
  min-height: 24px;
}

.tags-label,
.features-label {
  font-size: 12px;
  color: #606266;
  font-weight: 600;
  width: 60px;
  flex-shrink: 0;
  line-height: 24px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tags-content,
.features-content {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.tag-enhanced,
.feature-enhanced {
  font-size: 11px;
  height: 20px;
  line-height: 18px;
  padding: 0 6px;
}

.add-btn-enhanced {
  height: 20px;
  padding: 0 6px;
  font-size: 10px;
  border-style: dashed;
  line-height: 18px;
}

.input-enhanced {
  width: 80px;
}

.input-enhanced :deep(.el-input__inner) {
  height: 20px;
  font-size: 11px;
  padding: 0 6px;
  border: 1px solid #dcdfe6;
}

.tags-features-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 3px;
  align-items: center;
  min-height: 20px;
}

.tag-compact,
.feature-compact {
  font-size: 10px;
  height: 18px;
  line-height: 16px;
  padding: 0 4px;
}

.add-buttons-compact {
  margin-left: 4px;
}

.add-btn-compact {
  height: 18px;
  padding: 0 4px;
  font-size: 10px;
  border-style: dashed;
  line-height: 16px;
}

.input-compact {
  width: 60px;
  margin-left: 4px;
}

.input-compact :deep(.el-input__inner) {
  height: 18px;
  font-size: 10px;
  padding: 0 4px;
  border: 1px solid #dcdfe6;
}

/* 保持原有样式兼容性 */
.file-tags {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.tag-item {
  font-size: 11px;
}

.tag-input {
  width: 80px;
}

.add-tag-btn {
  height: 24px;
  padding: 0 8px;
  font-size: 11px;
  border-style: dashed;
}

.file-features {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.feature-item {
  font-size: 11px;
}

.feature-input {
  width: 80px;
}

.add-feature-btn {
  height: 24px;
  padding: 0 8px;
  font-size: 11px;
  border-style: dashed;
}

.ad-count {
  font-size: 11px;
  color: #e6a23c;
  background: #fdf6ec;
  padding: 3px 8px;
  border-radius: 3px;
  white-space: nowrap;
}

.table-select {
  width: 100%;
}

.table-select :deep(.el-input__inner) {
  border: 1px solid transparent;
  background: transparent;
  text-align: center;
}

.table-select :deep(.el-input__inner):hover {
  border-color: #c0c4cc;
  background: #fff;
}

.table-select :deep(.el-input__inner):focus {
  border-color: #409eff;
  background: #fff;
}

.table-simple-input {
  width: 100%;
}

.table-simple-input :deep(.el-input__inner) {
  border: 1px solid transparent;
  background: transparent;
  text-align: center;
  padding: 4px 6px;
}

.table-simple-input :deep(.el-input__inner):hover {
  border-color: #c0c4cc;
  background: #fff;
}

.table-simple-input :deep(.el-input__inner):focus {
  border-color: #409eff;
  background: #fff;
}

.file-urls,
.preview-urls {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.url-btn {
  width: 100%;
  font-size: 11px;
  padding: 4px 8px;
  height: 24px;
}

/* 表格行高度调整 - 卡片式布局 */
:deep(.el-table .el-table__row) {
  height: auto;
  border-bottom: 2px solid #f0f2f5;
}

:deep(.el-table .el-table__row:hover) {
  background: #f8f9fa !important;
}

:deep(.el-table .el-table__row:hover .file-card-content) {
  border-color: #409eff;
  box-shadow: 0 3px 8px rgba(64, 158, 255, 0.2);
}

:deep(.el-table .el-table__cell) {
  padding: 12px 8px;
  vertical-align: top;
  border-bottom: none;
}

:deep(.el-table .el-table__header .el-table__cell) {
  padding: 12px 8px;
  font-size: 13px;
  font-weight: 600;
  background: #f5f7fa;
  border-bottom: 2px solid #e4e7ed;
}

:deep(.el-table::before) {
  display: none;
}

:deep(.el-table .el-table__body tr:last-child td) {
  border-bottom: 2px solid #f0f2f5;
}

/* 开关样式调整 */
:deep(.el-switch) {
  --el-switch-on-color: #67c23a;
  --el-switch-off-color: #f56c6c;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.action-buttons .el-button {
  margin: 0;
}

.time-info {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

/* 表格整体样式 */
.file-table-enhanced {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-table-enhanced :deep(.el-table__inner-wrapper) {
  border-radius: 8px;
}

.file-table-enhanced :deep(.el-table__header-wrapper) {
  border-radius: 8px 8px 0 0;
}

/* 移除斑马纹，使用卡片分隔 */
.file-table-enhanced :deep(.el-table__row.el-table__row--striped) {
  background: transparent;
}

.file-table-enhanced :deep(.el-table__row.el-table__row--striped:hover) {
  background: #f8f9fa !important;
}

/* 清新的文件卡片样式 */
.file-card-clean {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.file-card-clean:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.file-card-clean.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

/* 卡片头部 */
.card-header-clean {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f2f5;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.file-checkbox {
  margin-top: 4px;
  flex-shrink: 0;
}

.file-title-area {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.title-input-clean {
  width: 100%;
}

.title-input-clean .el-input__wrapper {
  box-shadow: none;
  border: 1px solid transparent;
  background: transparent;
  padding: 4px 8px;
}

.title-input-clean .el-input__wrapper:hover {
  border-color: #c0c4cc;
  background: #fafafa;
}

.title-input-clean .el-input__wrapper.is-focus {
  border-color: #409eff;
  background: white;
  box-shadow: 0 0 0 1px #409eff inset;
}

.title-input-clean .el-input__inner {
  font-weight: 500;
  color: #303133;
}

.file-meta-clean {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #909399;
  flex-wrap: wrap;
}

.meta-item {
  white-space: nowrap;
  padding: 2px 6px;
  background: #f5f7fa;
  border-radius: 4px;
}

.header-right {
  flex-shrink: 0;
}

.status-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
}

/* 卡片主体 */
.card-body-clean {
  padding: 16px;
}

/* 分类行 */
.category-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.category-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.category-group label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.category-group .el-select {
  width: 100%;
}

/* 数据和操作行 */
.data-actions-row {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  gap: 16px;
  flex-wrap: wrap;
}

.data-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.data-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 70px;
}

.data-item label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
  text-align: center;
}

.data-item .el-input {
  width: 70px;
}

.actions-group {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

/* 扩展区域 */
.card-extra-clean {
  border-top: 1px solid #f0f2f5;
  padding: 16px;
  background: #fafbfc;
}

.desc-area-clean {
  margin-bottom: 16px;
}

.desc-area-clean label {
  display: block;
  font-size: 12px;
  color: #606266;
  font-weight: 500;
  margin-bottom: 8px;
}

.tags-features-clean {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.tags-section-clean,
.features-section-clean {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tags-section-clean label,
.features-section-clean label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.tags-content-clean,
.features-content-clean {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
  min-height: 32px;
}

/* 展开/收起按钮 */
.card-toggle-clean {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  cursor: pointer;
  font-size: 13px;
  color: #6c757d;
  transition: all 0.3s ease;
}

.card-toggle-clean:hover {
  background: #e3f2fd;
  color: #1976d2;
}

/* 预览相关样式 */
/* 预览图区域样式 */
.preview-section {
  margin-top: 8px;
  margin-bottom: 4px;
}

.preview-trigger-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.preview-trigger-wrapper:hover {
  background: #e3f2fd;
  border-color: #409eff;
}

.preview-trigger {
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.preview-trigger.loading {
  color: #909399;
}

.preview-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.preview-count {
  font-size: 10px;
  color: #409eff;
  background: #fff;
  border: 1px solid #409eff;
  border-radius: 10px;
  padding: 1px 6px;
  min-width: 16px;
  text-align: center;
  line-height: 1.4;
  font-weight: 600;
}

.preview-trigger-wrapper:hover .preview-count {
  background: #409eff;
  color: white;
}

/* 简化的预览图弹窗 */
.preview-popover-simple {
  max-width: 600px;
  padding: 8px;
}

.preview-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  justify-content: center;
  color: #909399;
}

.preview-grid-simple {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 8px;
  max-height: 500px;
  overflow-y: auto;
}

.preview-img-simple {
  width: 100%;
  height: auto;
  max-height: 240px; /* A4比例，适合阅读 */
  object-fit: contain; /* 保持完整比例 */
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  background: #fafafa;
  transition: all 0.3s ease;
}

.preview-img-simple:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.no-preview {
  text-align: center;
  color: #c0c4cc;
  padding: 16px;
}

/* 简化后不需要这些复杂的预览对话框样式了 */

/* 响应式设计 */
@media (max-width: 1200px) {
  .category-row {
    grid-template-columns: repeat(2, 1fr);
  }

  .data-actions-row {
    flex-direction: column;
    align-items: stretch;
  }

  .actions-group {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .filter-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .sort-tools-inline,
  .page-size-tools {
    margin-left: 0;
    padding-left: 0;
    border-left: none;
    justify-content: center;
  }

  .card-header-clean {
    flex-direction: column;
    gap: 12px;
  }

  .category-row {
    grid-template-columns: 1fr;
  }

  .tags-features-clean {
    grid-template-columns: 1fr;
  }
}
</style>
