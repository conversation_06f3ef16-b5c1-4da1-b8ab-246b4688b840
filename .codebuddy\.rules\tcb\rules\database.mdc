---
description: 云开发云数据库（NoSQL）参考指南
alwaysApply: false
---
# 数据库操作规则

## CloudBase 数据库注意事项
1. CloudBase数据库doc(id).get()返回的data是数组，需用data[0]获取文档内容
2. 更新文档时，避免直接存储复杂对象，应提取和保存简单值
3. 错误处理应返回error.message而非整个error对象，避免循环引用
4. 使用new Date()替代db.serverDate()创建时间戳
5. 对于有数据库归属的情况，检查和更新应通过云函数处理，避免数据库权限问题
6. 云开发的云数据或者 mongodb不能在null值上创建新的嵌套字段，必要时可以用set()替代update()并删除_id

## 数据库权限管理
1. 云开发的数据库访问是有权限的，默认的基础权限有：
   - 仅创建者可写，所有人可读
   - 仅创建者可读写
   - 仅管理端可写，所有人可读
   - 仅管理端可读写
2. 如果直接从 web 端或者小程序端请求数据库，需要考虑配置合适的数据库权限
3. 在云函数中，默认没有权限控制
4. 跨数据库集合的操作必须通过云函数实现