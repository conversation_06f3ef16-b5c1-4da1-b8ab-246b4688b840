import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('发送请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('收到响应:', response.status, response.config.url)
    return response.data
  },
  error => {
    console.error('响应错误:', error)
    const message = error.response?.data?.error || error.message || '网络错误'
    return Promise.reject(new Error(message))
  }
)

// 系统配置API
export const configAPI = {
  // 获取所有配置
  getAllConfigs() {
    return api.get('/config')
  },

  // 获取单个配置
  getConfig(key) {
    return api.get(`/config/${key}`)
  },

  // 更新配置
  updateConfig(key, value, type = 'string') {
    return api.put(`/config/${key}`, { value, type })
  },

  // 批量更新配置
  batchUpdateConfigs(configs) {
    return api.post('/config/batch', { configs })
  },

  // 创建配置
  createConfig(configData) {
    return api.post('/config', configData)
  },

  // 删除配置
  deleteConfig(key) {
    return api.delete(`/config/${key}`)
  }
}

export default api
