// 预览图生成服务
const fs = require('fs')
const path = require('path')
const sharp = require('sharp')
const { app: cloudbase } = require('../config/cloudbase')
const { v4: uuidv4 } = require('uuid')

class PreviewService {
  constructor() {
    this.app = cloudbase

    // 预览图配置
    this.previewConfig = {
      width: 400,
      height: 600,
      quality: 80,
      format: 'jpeg',
      maxPages: 10  // 增加到10页
    }
  }

  // 生成PDF预览图（需要安装poppler-utils）
  async generatePdfPreviews(pdfPath, dateFolder = null, fileName = null) {
    try {
      console.log('开始生成PDF预览图:', pdfPath)

      // 检查是否安装了pdf-poppler
      let pdfPoppler
      try {
        pdfPoppler = require('pdf-poppler')
        console.log('pdf-poppler库加载成功')
      } catch (error) {
        console.warn('pdf-poppler未安装，跳过PDF预览图生成')
        return {
          success: true,
          data: [],
          message: 'PDF预览图生成功能需要安装poppler-utils'
        }
      }

      const outputDir = path.join(__dirname, '../../uploads/temp/previews')
      console.log('预览图输出目录:', outputDir)

      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true })
        console.log('创建预览图目录成功')
      }

      // 获取PDF页数信息来确定要转换的页面数
      const pdfInfo = await pdfPoppler.info(pdfPath)
      const totalPages = parseInt(pdfInfo.pages) || 0
      const maxPages = Math.min(totalPages, 3) // 只转换前3页

      console.log(`PDF总页数: ${totalPages}, 将转换前 ${maxPages} 页`)

      const options = {
        format: 'jpeg',
        out_dir: outputDir,
        out_prefix: `preview_${uuidv4()}`,
        page: maxPages > 0 ? `1-${maxPages}` : null, // 只转换前N页
        quality: this.previewConfig.quality
      }

      // 生成预览图
      // 生成预览图
      const convertResult = await pdfPoppler.convert(pdfPath, options)

      // 检查实际生成的文件
      console.log('🔍 检查预览图输出目录:', outputDir)
      const outputFiles = fs.readdirSync(outputDir).filter(file => 
        file.startsWith(options.out_prefix) && file.endsWith('.jpg')
      )
      console.log('📁 实际生成的文件:', outputFiles)

      const previewImages = []

      // 处理实际生成的文件
      for (let i = 0; i < Math.min(outputFiles.length, maxPages); i++) {
        const actualFileName = outputFiles[i]
        const imagePath = path.join(outputDir, actualFileName)

        if (fs.existsSync(imagePath)) {
          console.log(`✅ 处理预览图 ${i + 1}/${maxPages}:`, imagePath)
          
          // 压缩和调整尺寸
          const compressedPath = await this.compressImage(imagePath)

          // 上传到云存储（使用标准格式：对象数组）
          const uploadResult = await this.uploadPreviewImage(compressedPath, i + 1, dateFolder, fileName)

          if (uploadResult.success) {
            // 使用标准格式存储预览图：{order: 页码, url: 云存储URL}
            previewImages.push({
              order: i + 1,
              url: uploadResult.data.fileID
            })
            console.log(`✅ 预览图 ${i + 1} 上传成功:`, {
              页码: i + 1,
              文件ID: uploadResult.data.fileID,
              云存储路径: uploadResult.data.cloudPath
            })
          } else {
            console.error(`❌ 预览图 ${i + 1} 上传失败:`, uploadResult.error)
          }

          // 清理临时文件
          try {
            fs.unlinkSync(imagePath)
            if (compressedPath !== imagePath) {
              fs.unlinkSync(compressedPath)
            }
            console.log(`🗑️ 已清理临时文件: ${actualFileName}`)
          } catch (cleanupError) {
            console.warn('清理预览图临时文件失败:', cleanupError.message)
          }
        } else {
          console.warn(`⚠️ 预览图文件不存在: ${imagePath}`)
        }
      }

      // 如果没有生成任何预览图，尝试其他可能的文件名格式
      if (previewImages.length === 0 && outputFiles.length === 0) {
        console.log('🔍 尝试查找其他可能的文件名格式...')
        const allFiles = fs.readdirSync(outputDir)
        console.log('📂 输出目录所有文件:', allFiles)
        
        // 查找可能的预览图文件
        const possibleFiles = allFiles.filter(file => 
          file.includes('preview') && (file.endsWith('.jpg') || file.endsWith('.jpeg'))
        )
        console.log('🎯 可能的预览图文件:', possibleFiles)
      }

      console.log(`📊 预览图生成完成: 成功生成 ${previewImages.length}/${maxPages} 张预览图`)

      return {
        success: true,
        data: previewImages
      }
    } catch (error) {
      console.error('生成PDF预览图失败:', error)
      return {
        success: false,
        error: error.message,
        data: []
      }
    }
  }

  // 压缩图片
  async compressImage(imagePath) {
    try {
      const outputPath = imagePath.replace('.jpg', '_compressed.jpg')

      await sharp(imagePath)
        .resize(this.previewConfig.width, this.previewConfig.height, {
          fit: 'inside',
          withoutEnlargement: true
        })
        .jpeg({
          quality: this.previewConfig.quality,
          progressive: true
        })
        .toFile(outputPath)

      return outputPath
    } catch (error) {
      console.error('压缩图片失败:', error)
      return imagePath // 返回原图片路径
    }
  }

  // 上传预览图到云存储
  async uploadPreviewImage(imagePath, pageNumber, dateFolder = null, fileName = null) {
    try {
      let previewFileName, cloudPath

      console.log('uploadPreviewImage参数检查:', {
        dateFolder: dateFolder,
        fileName: fileName,
        dateFolderType: typeof dateFolder,
        fileNameType: typeof fileName,
        dateFolderTruthy: !!dateFolder,
        fileNameTruthy: !!fileName
      })

      if (dateFolder && fileName) {
        // 预览图与文件在同一路径下：原文件名_page页码.jpg
        const ext = path.extname(fileName)
        const nameWithoutExt = path.basename(fileName, ext)
        previewFileName = `${nameWithoutExt}_page${pageNumber}.jpg`
        cloudPath = `files/${dateFolder}/${previewFileName}`

        console.log('预览图路径:', {
          文件名: fileName,
          预览图名: previewFileName,
          云存储路径: cloudPath
        })
      } else {
        // 兼容旧的命名规则
        previewFileName = `preview_${uuidv4()}_page${pageNumber}.jpg`
        cloudPath = `files/previews/${previewFileName}`
      }

      console.log('预览图上传信息:', {
        pageNumber,
        dateFolder,
        originalFileName: fileName,
        previewFileName,
        cloudPath
      })

      const fileStream = fs.createReadStream(imagePath)

      const result = await this.app.uploadFile({
        cloudPath,
        fileContent: fileStream
      })

      return {
        success: true,
        data: {
          fileID: result.fileID,
          cloudPath
        }
      }
    } catch (error) {
      console.error('上传预览图失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 生成Word文档预览图（简化版本）
  async generateWordPreviews(wordPath, dateFolder = null, fileName = null) {
    try {
      // Word文档预览图生成比较复杂，需要LibreOffice或其他工具
      // 这里返回空数组，实际部署时可以考虑使用LibreOffice headless模式
      console.log('Word文档预览图生成功能待实现')

      return {
        success: true,
        data: [],
        message: 'Word文档预览图生成功能待实现'
      }
    } catch (error) {
      console.error('生成Word预览图失败:', error)
      return {
        success: false,
        error: error.message,
        data: []
      }
    }
  }

  // 生成PowerPoint预览图（简化版本）
  async generatePptPreviews(pptPath, dateFolder = null, fileName = null) {
    try {
      // PowerPoint预览图生成需要LibreOffice或其他工具
      console.log('PowerPoint预览图生成功能待实现')

      return {
        success: true,
        data: [],
        message: 'PowerPoint预览图生成功能待实现'
      }
    } catch (error) {
      console.error('生成PowerPoint预览图失败:', error)
      return {
        success: false,
        error: error.message,
        data: []
      }
    }
  }

  // 根据文件类型生成预览图
  async generatePreviews(filePath, fileType, dateFolder = null, fileName = null) {
    try {
      console.log('generatePreviews调用参数:', {
        filePath,
        fileType,
        dateFolder,
        fileName,
        dateFolderType: typeof dateFolder,
        fileNameType: typeof fileName
      })

      switch (fileType.toLowerCase()) {
        case 'pdf':
          return await this.generatePdfPreviews(filePath, dateFolder, fileName)

        case 'doc':
        case 'docx':
          return await this.generateWordPreviews(filePath, dateFolder, fileName)

        case 'ppt':
        case 'pptx':
          return await this.generatePptPreviews(filePath, dateFolder, fileName)

        default:
          return {
            success: true,
            data: [],
            message: `不支持 ${fileType} 格式的预览图生成`
          }
      }
    } catch (error) {
      console.error('生成预览图失败:', error)
      return {
        success: false,
        error: error.message,
        data: []
      }
    }
  }

  // 删除预览图
  async deletePreviews(previewImages) {
    try {
      if (!previewImages || previewImages.length === 0) {
        return { success: true }
      }

      const fileIds = previewImages.map(img => img.url)

      const result = await this.app.deleteFile({
        fileList: fileIds
      })

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('删除预览图失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 获取预览图下载链接
  async getPreviewUrls(previewImages) {
    try {
      if (!previewImages || previewImages.length === 0) {
        return {
          success: true,
          data: []
        }
      }

      const fileList = previewImages.map(img => ({
        fileID: img.url,
        maxAge: 3600 // 1小时有效期
      }))

      const result = await this.app.getTempFileURL({ fileList })

      const previewUrls = []
      if (result.fileList) {
        result.fileList.forEach((file, index) => {
          if (file.code === 'SUCCESS') {
            previewUrls.push({
              ...previewImages[index],
              tempUrl: file.tempFileURL
            })
          }
        })
      }

      return {
        success: true,
        data: previewUrls
      }
    } catch (error) {
      console.error('获取预览图链接失败:', error)
      return {
        success: false,
        error: error.message,
        data: []
      }
    }
  }

  // 检查系统依赖
  checkDependencies() {
    const dependencies = {
      'pdf-poppler': false,
      'sharp': false,
      'poppler-utils': false
    }

    try {
      require('pdf-poppler')
      dependencies['pdf-poppler'] = true
      console.log('✅ pdf-poppler库已安装')
    } catch (error) {
      console.warn('❌ pdf-poppler未安装，PDF预览图功能不可用')
      console.warn('安装命令: npm install pdf-poppler')
    }

    try {
      require('sharp')
      dependencies['sharp'] = true
      console.log('✅ sharp库已安装')
    } catch (error) {
      console.warn('❌ sharp未安装，图片处理功能不可用')
      console.warn('安装命令: npm install sharp')
    }

    // 检查poppler-utils系统依赖
    try {
      const { execSync } = require('child_process')
      execSync('pdftoppm -h', { stdio: 'ignore' })
      dependencies['poppler-utils'] = true
      console.log('✅ poppler-utils系统依赖已安装')
    } catch (error) {
      console.warn('❌ poppler-utils系统依赖未安装，PDF转图片功能不可用')
      console.warn('Windows安装: 下载poppler-utils并添加到PATH')
      console.warn('Linux安装: sudo apt-get install poppler-utils')
      console.warn('macOS安装: brew install poppler')
    }

    return dependencies
  }

  // 重新生成文件的预览图
  async regeneratePreviewImages(fileId, filePath, fileType, dateFolder, fileName) {
    try {
      console.log(`开始重新生成文件 ${fileId} 的预览图`)

      // 检查依赖
      const deps = this.checkDependencies()
      if (!deps['pdf-poppler'] || !deps['sharp']) {
        return {
          success: false,
          error: '缺少必要的依赖库，无法生成预览图'
        }
      }

      // 删除旧的预览图（如果存在）
      const { db } = require('../config/cloudbase')
      const filesCollection = db.collection('files')
      
      const fileResult = await filesCollection.doc(fileId).get()
      if (fileResult.data.length > 0 && fileResult.data[0].preview_images) {
        const oldPreviews = fileResult.data[0].preview_images
        if (oldPreviews.length > 0) {
          console.log(`删除旧预览图: ${oldPreviews.length} 张`)
          try {
            await this.app.deleteFile({ fileList: oldPreviews })
          } catch (deleteError) {
            console.warn('删除旧预览图失败:', deleteError.message)
          }
        }
      }

      // 生成新的预览图
      const result = await this.generatePreviews(filePath, fileType, dateFolder, fileName)
      
      if (result.success && result.data.length > 0) {
        // 更新数据库中的预览图URL
        await filesCollection.doc(fileId).update({
          preview_images: result.data
        })

        console.log(`文件 ${fileId} 预览图重新生成成功: ${result.data.length} 张`)
        return {
          success: true,
          data: result.data,
          message: `成功生成 ${result.data.length} 张预览图`
        }
      } else {
        return {
          success: false,
          error: result.error || '预览图生成失败'
        }
      }
    } catch (error) {
      console.error('重新生成预览图失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 批量检查和修复预览图
  async batchCheckPreviewImages() {
    try {
      console.log('开始批量检查预览图状态...')
      
      const { db } = require('../config/cloudbase')
      const filesCollection = db.collection('files')
      
      // 获取所有活跃的PDF文件
      const result = await filesCollection
        .where({ 
          status: 'active',
          file_type: 'pdf'
        })
        .get()

      const files = result.data
      const checkResults = []

      for (const file of files) {
        const fileCheck = {
          fileId: file._id,
          title: file.title,
          hasPreviewImages: !!(file.preview_images && file.preview_images.length > 0),
          previewCount: file.preview_images ? file.preview_images.length : 0,
          needsRegeneration: false,
          validPreviews: 0
        }

        // 检查预览图是否存在于云存储中
        if (file.preview_images && file.preview_images.length > 0) {
          try {
            const fileList = file.preview_images.map(url => ({
              fileID: url,
              maxAge: 60
            }))

            const urlResult = await this.app.getTempFileURL({ fileList })
            
            if (urlResult.fileList) {
              fileCheck.validPreviews = urlResult.fileList.filter(item => 
                item.code === 'SUCCESS'
              ).length
              
              fileCheck.needsRegeneration = fileCheck.validPreviews === 0
            }
          } catch (error) {
            fileCheck.needsRegeneration = true
          }
        } else {
          fileCheck.needsRegeneration = true
        }

        checkResults.push(fileCheck)
      }

      const needsRegeneration = checkResults.filter(item => item.needsRegeneration)
      
      console.log(`预览图检查完成: 总计 ${files.length} 个PDF文件，${needsRegeneration.length} 个需要重新生成预览图`)

      return {
        success: true,
        data: {
          total: files.length,
          needsRegeneration: needsRegeneration.length,
          results: checkResults
        }
      }
    } catch (error) {
      console.error('批量检查预览图失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
}

module.exports = new PreviewService()
