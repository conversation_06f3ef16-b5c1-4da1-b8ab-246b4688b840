<template>
  <el-dialog
    v-model="visible"
    title="上传文件"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <!-- 文件上传区域 -->
      <el-form-item label="选择文件" prop="files">
        <el-upload
          ref="uploadRef"
          :file-list="fileList"
          :auto-upload="false"
          :multiple="uploadMode === 'batch'"
          :accept="acceptTypes"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          drag
          class="upload-area"
        >
          <div class="upload-content">
            <el-icon class="upload-icon"><UploadFilled /></el-icon>
            <div class="upload-text">
              <p>将文件拖到此处，或<em>点击上传</em></p>
              <p class="upload-tip">
                支持 PDF、DOC、DOCX、PPT、PPTX 格式，单个文件不超过 50MB
              </p>
              <p class="upload-tip" style="color: #67C23A; margin-top: 8px;">
                <el-icon><InfoFilled /></el-icon>
                文件将保持原始文件名，支持中文文件名
              </p>
            </div>
          </div>
        </el-upload>
      </el-form-item>

      <!-- 文件名预览区域 -->
      <el-form-item v-if="fileList.length > 0" label="文件预览">
        <div class="file-preview-list">
          <div
            v-for="(file, index) in fileList"
            :key="index"
            class="file-preview-item"
          >
            <div class="file-info">
              <el-icon class="file-icon">
                <Document v-if="isDocumentFile(file.name)" />
                <Picture v-else-if="isPictureFile(file.name)" />
                <Files v-else />
              </el-icon>
              <div class="file-details">
                <div class="file-name" :title="file.name">{{ file.name }}</div>
                <div class="file-meta">
                  <span class="file-size">{{ formatFileSize(file.size) }}</span>
                  <span class="file-type">{{ getFileExtension(file.name).toUpperCase() }}</span>
                </div>
              </div>
            </div>
            <div class="file-actions">
              <el-button
                type="danger"
                size="small"
                :icon="Delete"
                circle
                @click="removeFile(index)"
              />
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 上传模式切换 -->
      <el-form-item label="上传模式">
        <el-radio-group v-model="uploadMode" @change="handleModeChange">
          <el-radio label="single">单文件上传</el-radio>
          <el-radio label="batch">批量上传</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 文件元数据 -->
      <div class="metadata-section">
        <h4>文件信息</h4>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="标题" prop="title">
              <el-input
                v-model="form.title"
                placeholder="请输入文件标题"
                :disabled="uploadMode === 'batch'"
              />
              <div v-if="uploadMode === 'batch'" class="form-tip">
                批量上传时将使用文件名作为标题
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="年级" prop="grade">
              <el-select v-model="form.grade" placeholder="选择年级" clearable>
                <el-option label="幼升小" value="幼升小" />
                <el-option label="一年级" value="一年级" />
                <el-option label="二年级" value="二年级" />
                <el-option label="三年级" value="三年级" />
                <el-option label="四年级" value="四年级" />
                <el-option label="五年级" value="五年级" />
                <el-option label="六年级" value="六年级" />
                <el-option label="小升初" value="小升初" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="科目" prop="subject">
              <el-select v-model="form.subject" placeholder="选择科目">
                <el-option label="语文" value="语文" />
                <el-option label="数学" value="数学" />
                <el-option label="英语" value="英语" />
                <el-option label="科学" value="科学" />
                <el-option label="音乐" value="音乐" />
                <el-option label="美术" value="美术" />
                <el-option label="体育" value="体育" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="册别" prop="volume">
              <el-select v-model="form.volume" placeholder="选择册别">
                <el-option label="上册" value="上册" />
                <el-option label="下册" value="下册" />
                <el-option label="全册" value="全册" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="板块" prop="section">
              <el-select v-model="form.section" placeholder="选择板块">
                <el-option-group label="常规资料">
                  <el-option label="单元同步" value="单元同步" />
                  <el-option label="单元知识点" value="单元知识点" />
                  <el-option label="核心知识点" value="核心知识点" />
                  <el-option label="试卷" value="试卷" />
                  <el-option label="专项练习" value="专项练习" />
                </el-option-group>
                <el-option-group label="幼升小专区">
                  <el-option label="拼音启蒙" value="拼音启蒙" />
                  <el-option label="认识数字" value="认识数字" />
                  <el-option label="习惯养成" value="习惯养成" />
                  <el-option label="学科启蒙" value="学科启蒙" />
                  <el-option label="知识科普" value="知识科普" />
                </el-option-group>
                <el-option-group label="小升初专区">
                  <el-option label="语文冲刺" value="语文冲刺" />
                  <el-option label="数学冲刺" value="数学冲刺" />
                  <el-option label="英语强化" value="英语强化" />
                  <el-option label="真题模拟" value="真题模拟" />
                  <el-option label="面试准备" value="面试准备" />
                </el-option-group>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类">
              <el-select v-model="form.category" placeholder="选择分类">
                <el-option label="常规资料" value="regular" />
                <el-option label="升学专区" value="upgrade" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="广告次数">
              <el-input-number
                v-model="form.ad_required_count"
                :min="1"
                :max="10"
                placeholder="下载前需观看的广告次数"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序权重">
              <el-input-number
                v-model="form.sort_order"
                :min="0"
                placeholder="数值越大越靠前"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="标签">
              <el-input
                v-model="form.tags"
                placeholder="多个标签用逗号分隔"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="文件特征">
              <el-select
                v-model="form.features"
                placeholder="选择文件特征"
                multiple
                collapse-tags
                collapse-tags-tooltip
                style="width: 100%"
              >
                <el-option label="高清版" value="高清版" />
                <el-option label="彩色版" value="彩色版" />
                <el-option label="黑白版" value="黑白版" />
                <el-option label="可打印" value="可打印" />
                <el-option label="含答案" value="含答案" />
                <el-option label="含解析" value="含解析" />
                <el-option label="教师版" value="教师版" />
                <el-option label="学生版" value="学生版" />
                <el-option label="完整版" value="完整版" />
                <el-option label="精简版" value="精简版" />
                <el-option label="重点标注" value="重点标注" />
                <el-option label="配套音频" value="配套音频" />
                <el-option label="配套视频" value="配套视频" />
                <el-option label="互动练习" value="互动练习" />
                <el-option label="思维导图" value="思维导图" />
              </el-select>
              <div class="form-tip">
                可选择多个特征，帮助用户更好地了解文件内容
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入文件描述"
          />
        </el-form-item>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress">
        <h4>上传进度</h4>
        <el-progress
          :percentage="uploadProgress"
          :status="uploadStatus"
          :stroke-width="8"
        />
        <p class="progress-text">{{ progressText }}</p>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="uploading"
          :disabled="fileList.length === 0"
          @click="handleUpload"
        >
          {{ uploading ? '上传中...' : '开始上传' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, InfoFilled, Document, Picture, Files, Delete } from '@element-plus/icons-vue'
import { fileApi } from '@/api/files'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const uploadRef = ref()
const fileList = ref([])
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref('')
const progressText = ref('')

const uploadMode = ref('single')

// 表单数据
const form = reactive({
  title: '',
  description: '',
  grade: '',
  subject: '',
  volume: '',
  section: '',
  category: 'regular',
  tags: '',
  features: [],
  ad_required_count: 1,
  sort_order: 0
})

// 表单验证规则
const rules = {
  subject: [{ required: true, message: '请选择科目', trigger: 'change' }],
  volume: [{ required: true, message: '请选择册别', trigger: 'change' }],
  section: [{ required: true, message: '请选择板块', trigger: 'change' }]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const acceptTypes = computed(() => {
  return '.pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx'
})

// 方法
const beforeUpload = (file) => {
  const isValidType = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ].includes(file.type)

  if (!isValidType) {
    ElMessage.error('只支持 PDF、DOC、DOCX、PPT、PPTX、XLS、XLSX 格式的文件')
    return false
  }

  const isValidSize = file.size / 1024 / 1024 < 50
  if (!isValidSize) {
    ElMessage.error('文件大小不能超过 50MB')
    return false
  }

  return false // 阻止自动上传
}

const handleFileChange = (file, files) => {
  fileList.value = files

  // 单文件上传时自动填充标题
  if (uploadMode.value === 'single' && files.length === 1) {
    const fileName = file.name.replace(/\.[^/.]+$/, '')
    form.title = fileName
  }
}

const handleFileRemove = (file, files) => {
  fileList.value = files
}

// 移除文件
const removeFile = (index) => {
  fileList.value.splice(index, 1)
}

// 判断文件类型
const isDocumentFile = (fileName) => {
  const ext = getFileExtension(fileName).toLowerCase()
  return ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(ext)
}

const isPictureFile = (fileName) => {
  const ext = getFileExtension(fileName).toLowerCase()
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)
}

// 获取文件扩展名
const getFileExtension = (fileName) => {
  const lastDot = fileName.lastIndexOf('.')
  return lastDot > 0 ? fileName.substring(lastDot + 1) : ''
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const handleModeChange = () => {
  fileList.value = []
  uploadRef.value?.clearFiles()

  if (uploadMode.value === 'batch') {
    form.title = ''
  }
}

const handleUpload = async () => {
  try {
    await formRef.value.validate()

    if (fileList.value.length === 0) {
      ElMessage.error('请选择要上传的文件')
      return
    }

    uploading.value = true
    uploadProgress.value = 0
    uploadStatus.value = ''

    const files = fileList.value.map(item => item.raw)

    if (uploadMode.value === 'single') {
      await uploadSingleFile(files[0])
    } else {
      await uploadBatchFiles(files)
    }

    ElMessage.success('文件上传成功')
    emit('success')
    handleClose()

  } catch (error) {
    uploadStatus.value = 'exception'
    ElMessage.error('上传失败: ' + error.message)
  } finally {
    uploading.value = false
  }
}

const uploadSingleFile = async (file) => {
  progressText.value = `正在上传: ${file.name}`

  // 处理 features 数组转换为字符串
  const uploadData = {
    ...form,
    features: Array.isArray(form.features) ? form.features.join(',') : form.features
  }

  const result = await fileApi.uploadFile(file, uploadData, (progressEvent) => {
    uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
  })

  if (result.success) {
    uploadStatus.value = 'success'
    uploadProgress.value = 100
    progressText.value = '上传完成'
  }
}

const uploadBatchFiles = async (files) => {
  progressText.value = `正在批量上传 ${files.length} 个文件...`

  // 处理 features 数组转换为字符串
  const uploadData = {
    ...form,
    features: Array.isArray(form.features) ? form.features.join(',') : form.features
  }

  const result = await fileApi.batchUploadFiles(files, uploadData, (progressEvent) => {
    uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
  })

  if (result.success) {
    uploadStatus.value = 'success'
    uploadProgress.value = 100
    progressText.value = `批量上传完成，成功: ${result.data.success}，失败: ${result.data.failed}`
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  formRef.value?.resetFields()
  fileList.value = []
  uploadRef.value?.clearFiles()
  uploading.value = false
  uploadProgress.value = 0
  uploadStatus.value = ''
  progressText.value = ''
  uploadMode.value = 'single'

  Object.assign(form, {
    title: '',
    description: '',
    grade: '',
    subject: '',
    volume: '',
    section: '',
    category: 'regular',
    tags: '',
    features: [],
    ad_required_count: 1,
    sort_order: 0
  })
}
</script>

<style scoped>
.upload-area {
  width: 100%;
}

.upload-content {
  padding: 40px 20px;
  text-align: center;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 8px 0;
  color: #606266;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
}

.metadata-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.metadata-section h4 {
  margin: 0 0 20px 0;
  color: #303133;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.upload-progress {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.upload-progress h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.progress-text {
  margin: 8px 0 0 0;
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}

/* 文件预览样式 */
.file-preview-list {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
  max-height: 200px;
  overflow-y: auto;
}

.file-preview-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}

.file-preview-item:last-child {
  border-bottom: none;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.file-icon {
  font-size: 24px;
  color: #409eff;
  margin-right: 12px;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.file-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.file-actions {
  flex-shrink: 0;
  margin-left: 12px;
}

/* 文件名保持提示样式 */
.upload-tip .el-icon {
  margin-right: 4px;
  vertical-align: middle;
}
</style>
