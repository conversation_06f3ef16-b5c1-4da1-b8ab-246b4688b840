// 用户反馈路由
const express = require('express')
const router = express.Router()
const feedbackController = require('../controllers/feedbackController')

// 获取反馈列表
router.get('/', feedbackController.getFeedbackList)

// 获取反馈统计数据
router.get('/stats', feedbackController.getFeedbackStats)

// 导出反馈数据
router.get('/export', feedbackController.exportFeedback)

// 获取反馈详情
router.get('/:id', feedbackController.getFeedbackDetail)

// 更新反馈状态
router.put('/:id/status', feedbackController.updateFeedbackStatus)

// 删除反馈
router.delete('/:id', feedbackController.deleteFeedback)

// 批量更新反馈状态
router.put('/batch/status', feedbackController.batchUpdateStatus)

// 批量删除反馈
router.delete('/batch', feedbackController.batchDeleteFeedback)

module.exports = router
