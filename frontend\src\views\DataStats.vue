<template>
  <div class="data-stats">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>📊 数据统计</h2>
        <p class="header-desc">查看文件管理系统的各项统计数据和分析报告</p>
      </div>
      <div class="header-right">
        <el-button @click="refreshStats" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="primary" @click="exportStats">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 统计面板 -->
    <FileStatsPanel :stats="statsData" :loading="loading" />

    <!-- 其他统计信息 -->
    <el-row :gutter="20" class="additional-stats">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>文件类型分布</span>
            </div>
          </template>
          <div class="file-type-stats">
            <div v-if="fileTypeStats.length > 0" class="type-list">
              <div
                v-for="item in fileTypeStats"
                :key="item.type"
                class="type-item"
              >
                <div class="type-info">
                  <span class="type-name">{{ item.type.toUpperCase() }}</span>
                  <span class="type-count">{{ item.count }} 个</span>
                </div>
                <div class="type-bar">
                  <div
                    class="type-bar-fill"
                    :style="{
                      width: getTypePercentage(item.count) + '%',
                      backgroundColor: getTypeColor(item.type)
                    }"
                  ></div>
                </div>
              </div>
            </div>
            <el-empty v-else description="暂无数据" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
            </div>
          </template>
          <div class="recent-activity">
            <div v-if="recentActivity.length > 0" class="activity-list">
              <div
                v-for="(activity, index) in recentActivity"
                :key="index"
                class="activity-item"
              >
                <div class="activity-icon">
                  <el-icon :style="{ color: getActivityColor(activity.type) }">
                    <component :is="getActivityIcon(activity.type)" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-meta">
                    <span v-if="activity.grade" class="activity-grade">{{ activity.grade }}</span>
                    <span v-if="activity.subject" class="activity-subject">{{ activity.subject }}</span>
                    <span class="activity-time">{{ formatTime(activity.time) }}</span>
                  </div>
                </div>
              </div>
            </div>
            <el-empty v-else description="暂无活动记录" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Download, Upload, Edit, Delete, View } from '@element-plus/icons-vue'
import { fileApi } from '@/api/files'
import FileStatsPanel from '@/components/FileStatsPanel.vue'

// 响应式数据
const loading = ref(false)
const statsData = reactive({})
const fileTypeStats = ref([])
const recentActivity = ref([])

// 计算属性
const totalDownloads = computed(() => {
  if (!statsData.downloadRanking) return 0
  return statsData.downloadRanking.reduce((total, item) => total + (item.download_count || 0), 0)
})

const totalViews = computed(() => {
  if (!statsData.downloadRanking) return 0
  return statsData.downloadRanking.reduce((total, item) => total + (item.view_count || 0), 0)
})

// 生命周期
onMounted(() => {
  loadStats() // 只需要调用loadStats，它会自动调用其他加载函数
})

// 方法
const loadStats = async () => {
  loading.value = true
  try {
    const result = await fileApi.getFileStats()
    if (result.success) {
      Object.assign(statsData, result.data)
      // 数据加载完成后，更新文件类型统计和最近活动
      loadFileTypeStats()
      loadRecentActivity()
    }
  } catch (error) {
    ElMessage.error('加载统计数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const loadFileTypeStats = () => {
  // 使用后端返回的真实文件类型统计数据
  if (statsData.fileTypeStats && statsData.fileTypeStats.length > 0) {
    fileTypeStats.value = statsData.fileTypeStats.map(item => ({
      type: item._id || '未知',
      count: item.count
    }))
  } else {
    fileTypeStats.value = []
  }
}

const loadRecentActivity = () => {
  // 使用后端返回的真实最近活动数据
  if (statsData.recentActivity && statsData.recentActivity.length > 0) {
    recentActivity.value = statsData.recentActivity.map(item => ({
      type: 'upload', // 都是上传活动
      title: `上传了新文件：${item.title}`,
      time: new Date(item.created_time),
      grade: item.grade,
      subject: item.subject
    }))
  } else {
    recentActivity.value = []
  }
}

const refreshStats = async () => {
  await loadStats()
  loadFileTypeStats()
  loadRecentActivity()
  ElMessage.success('数据已刷新')
}

const exportStats = () => {
  try {
    // 生成统计报告数据
    const reportData = {
      exportTime: new Date().toLocaleString('zh-CN'),
      summary: {
        totalFiles: statsData.total || 0,
        todayUploads: statsData.todayUploads || 0,
        totalDownloads: totalDownloads.value,
        totalViews: totalViews.value
      },
      categoryStats: statsData.categoryStats || [],
      gradeStats: statsData.gradeStats || [],
      subjectStats: statsData.subjectStats || [],
      fileTypeStats: fileTypeStats.value || [],
      downloadRanking: statsData.downloadRanking || [],
      recentActivity: recentActivity.value || []
    }

    // 生成CSV格式的报告
    let csvContent = '数据统计报告\n'
    csvContent += `导出时间,${reportData.exportTime}\n\n`

    // 概览统计
    csvContent += '概览统计\n'
    csvContent += '指标,数值\n'
    csvContent += `文件总数,${reportData.summary.totalFiles}\n`
    csvContent += `今日上传,${reportData.summary.todayUploads}\n`
    csvContent += `总下载量,${reportData.summary.totalDownloads}\n`
    csvContent += `总查看量,${reportData.summary.totalViews}\n\n`

    // 分类统计
    if (reportData.categoryStats.length > 0) {
      csvContent += '分类统计\n'
      csvContent += '分类,数量\n'
      reportData.categoryStats.forEach(item => {
        const categoryName = item._id === 'regular' ? '常规资料' : (item._id === 'upgrade' ? '升学专区' : item._id)
        csvContent += `${categoryName},${item.count}\n`
      })
      csvContent += '\n'
    }

    // 文件类型统计
    if (reportData.fileTypeStats.length > 0) {
      csvContent += '文件类型统计\n'
      csvContent += '类型,数量\n'
      reportData.fileTypeStats.forEach(item => {
        csvContent += `${item.type.toUpperCase()},${item.count}\n`
      })
      csvContent += '\n'
    }

    // 下载排行榜
    if (reportData.downloadRanking.length > 0) {
      csvContent += '下载排行榜\n'
      csvContent += '排名,文件标题,年级,科目,下载量,查看量\n'
      reportData.downloadRanking.forEach((item, index) => {
        csvContent += `${index + 1},"${item.title}",${item.grade},${item.subject},${item.download_count},${item.view_count}\n`
      })
    }

    // 创建下载链接
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `数据统计报告_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('统计报告导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败: ' + error.message)
  }
}

const getTypePercentage = (count) => {
  const total = fileTypeStats.value.reduce((sum, item) => sum + item.count, 0)
  return total > 0 ? Math.round((count / total) * 100) : 0
}

const getTypeColor = (type) => {
  const colors = {
    pdf: '#f56c6c',
    doc: '#409eff',
    docx: '#409eff',
    ppt: '#e6a23c',
    pptx: '#e6a23c',
    xls: '#67c23a',
    xlsx: '#67c23a'
  }
  return colors[type] || '#909399'
}

const getActivityIcon = (type) => {
  const icons = {
    upload: Upload,
    edit: Edit,
    delete: Delete,
    view: View
  }
  return icons[type] || View
}

const getActivityColor = (type) => {
  const colors = {
    upload: '#67c23a',
    edit: '#409eff',
    delete: '#f56c6c',
    view: '#909399'
  }
  return colors[type] || '#909399'
}

const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days > 0) {
    return `${days}天前`
  } else if (hours > 0) {
    return `${hours}小时前`
  } else if (minutes > 0) {
    return `${minutes}分钟前`
  } else {
    return '刚刚'
  }
}
</script>

<style scoped>
.data-stats {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.additional-stats {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-type-stats {
  min-height: 200px;
  padding: 10px 0;
}

.type-list {
  padding: 10px 0;
}

.type-item {
  margin-bottom: 16px;
}

.type-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.type-name {
  font-weight: 500;
  color: #303133;
}

.type-count {
  font-size: 14px;
  color: #606266;
}

.type-bar {
  height: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
}

.type-bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.recent-activity {
  min-height: 200px;
  padding: 10px 0;
}

.activity-list {
  padding: 10px 0;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.activity-icon {
  flex: 0 0 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.activity-grade,
.activity-subject {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  background: #f0f2f5;
  color: #606266;
}

.activity-grade {
  background: #e1f3d8;
  color: #67c23a;
}

.activity-subject {
  background: #ecf5ff;
  color: #409eff;
}

.activity-time {
  font-size: 12px;
  color: #909399;
}
</style>
