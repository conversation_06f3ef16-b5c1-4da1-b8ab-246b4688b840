# 云开发配置
CLOUDBASE_ENV_ID=your-cloudbase-env-id
CLOUDBASE_SECRET_ID=your-secret-id
CLOUDBASE_SECRET_KEY=your-secret-key

# 服务器配置
PORT=3000
NODE_ENV=development
HOST=localhost

# 安全配置
JWT_SECRET=your-jwt-secret-key-here
SESSION_SECRET=your-session-secret-key-here

# 文件上传配置
UPLOAD_MAX_SIZE=52428800
UPLOAD_ALLOWED_TYPES=pdf,doc,docx,ppt,pptx
UPLOAD_TEMP_DIR=./uploads/temp
UPLOAD_DEST_DIR=./uploads/files

# 数据库配置
DB_COLLECTION_FILES=files
DB_COLLECTION_FEEDBACK=feedback
DB_COLLECTION_CONFIG=system_config
DB_COLLECTION_DOWNLOAD_RECORDS=download_records

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=100

# API限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 文件处理配置
PREVIEW_IMAGE_WIDTH=400
PREVIEW_IMAGE_HEIGHT=600
PREVIEW_IMAGE_QUALITY=80
PDF_PREVIEW_PAGES=3

# 邮件配置（可选）
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
SMTP_FROM=<EMAIL>

# 监控配置（可选）
ENABLE_MONITORING=false
MONITORING_ENDPOINT=http://localhost:3001/metrics

# 开发调试配置
DEBUG_MODE=true
ENABLE_CORS=true
CORS_ORIGIN=http://localhost:5173
