@echo off
chcp 65001 >nul
title K12 Admin 环境设置

echo.
echo ========================================
echo    K12 Admin 环境设置向导
echo ========================================
echo.

:: 检查 Node.js 是否安装
echo [1/4] 检查 Node.js 环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装或版本过低
    echo.
    echo 请按照以下步骤安装 Node.js:
    echo 1. 访问 https://nodejs.org/
    echo 2. 下载 LTS 版本 ^(推荐 18.x^)
    echo 3. 安装完成后重新运行此脚本
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js 已安装
    node --version
)

echo.
echo [2/4] 检查项目文件...
if not exist "package.json" (
    echo ❌ 项目文件不完整，请检查是否在正确目录
    pause
    exit /b 1
)
if not exist "backend\package.json" (
    echo ❌ 后端项目文件缺失
    pause
    exit /b 1
)
if not exist "frontend\package.json" (
    echo ❌ 前端项目文件缺失
    pause
    exit /b 1
)
echo ✅ 项目文件检查通过

echo.
echo [3/4] 安装项目依赖...
echo 这可能需要几分钟时间，请耐心等待...
echo.

:: 安装根项目依赖
echo 正在安装根项目依赖...
call npm install
if errorlevel 1 (
    echo ❌ 根项目依赖安装失败
    echo 尝试清理缓存后重新安装...
    call npm cache clean --force
    call npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

:: 安装后端依赖
echo 正在安装后端依赖...
cd backend
call npm install
if errorlevel 1 (
    echo ❌ 后端依赖安装失败
    cd ..
    pause
    exit /b 1
)
cd ..

:: 安装前端依赖
echo 正在安装前端依赖...
cd frontend
call npm install
if errorlevel 1 (
    echo ❌ 前端依赖安装失败
    cd ..
    pause
    exit /b 1
)
cd ..

echo ✅ 所有依赖安装完成

echo.
echo [4/4] 环境验证...
call node check-env.js

echo.
echo ========================================
echo    🎉 K12 Admin 环境设置完成！
echo ========================================
echo.
echo 启动方式：
echo   方式一：双击 run.bat
echo   方式二：运行 npm run dev
echo.
echo 访问地址：http://localhost:8080
echo.
echo 如遇问题，请查看 README.md 或 DEPLOYMENT.md
echo.

set /p choice="是否立即启动系统？(Y/N): "
if /i "%choice%"=="Y" (
    echo.
    echo 正在启动 K12 Admin 系统...
    call run.bat
) else (
    echo.
    echo 设置完成，您可以随时运行 run.bat 启动系统
    pause
)
