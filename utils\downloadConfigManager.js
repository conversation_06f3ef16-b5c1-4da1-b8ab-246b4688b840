// 下载配置管理器
class DownloadConfigManager {
  constructor() {
    this.configCache = new Map()
    this.cacheExpireTime = 60 * 60 * 1000 // 1小时缓存
  }

  // 获取单个配置
  async getConfig(key, defaultValue = null) {
    // 检查缓存
    if (this.configCache.has(key)) {
      const cached = this.configCache.get(key)
      if (Date.now() - cached.time < this.cacheExpireTime) {
        return cached.value
      }
    }

    try {
      const db = wx.cloud.database()
      const result = await db.collection('system_configs')
        .where({
          key: key,
          category: 'download'
        })
        .get()

      if (result.data.length > 0) {
        const config = result.data[0]
        let value = config.value

        // 根据类型转换
        switch (config.type) {
          case 'number':
            value = parseInt(value)
            break
          case 'boolean':
            value = value === 'true'
            break
          case 'json':
            value = JSON.parse(value)
            break
        }

        // 缓存结果
        this.configCache.set(key, {
          value: value,
          time: Date.now()
        })

        return value
      }

      return defaultValue
    } catch (error) {
      console.error('获取配置失败:', error)
      return defaultValue
    }
  }

  // 批量获取配置
  async getBatchConfigs(keys) {
    const configs = {}

    try {
      const db = wx.cloud.database()
      const result = await db.collection('system_configs')
        .where({
          key: db.command.in(keys),
          category: 'download'
        })
        .get()

      // 转换配置值
      result.data.forEach(config => {
        let value = config.value

        switch (config.type) {
          case 'number':
            value = parseInt(value)
            break
          case 'boolean':
            value = value === 'true'
            break
          case 'json':
            value = JSON.parse(value)
            break
        }

        configs[config.key] = value
      })

      // 对于没有找到的配置，使用默认值
      keys.forEach(key => {
        if (!(key in configs)) {
          configs[key] = this.getDefaultValue(key)
        }
      })

      return configs
    } catch (error) {
      console.error('批量获取配置失败:', error)
      return this.getDefaultConfigs(keys)
    }
  }

  // 获取默认配置值
  getDefaultValue(key) {
    const defaults = {
      download_frequency_same_file_per_minute: 1,
      download_frequency_same_file_per_hour: 3,
      download_frequency_same_file_per_day: 10,
      download_user_total_per_hour: 10,
      download_user_total_per_day: 10,
      download_max_concurrent: 1,
      download_cache_expire_days: 365,
      download_security_enabled: true,
      download_temp_url_expire_minutes: 5,
      download_suspicious_threshold: 70,
      download_enable_local_cache: true,
      download_enable_cloud_sync: true
    }

    return defaults[key] || null
  }

  // 获取默认配置集合
  getDefaultConfigs(keys) {
    const result = {}
    keys.forEach(key => {
      result[key] = this.getDefaultValue(key)
    })
    return result
  }

  // 清除缓存
  clearCache() {
    this.configCache.clear()
  }

  // 刷新单个配置缓存
  refreshConfig(key) {
    this.configCache.delete(key)
  }

  // 获取所有下载配置
  async getAllDownloadConfigs() {
    try {
      const db = wx.cloud.database()
      const result = await db.collection('system_configs')
        .where({
          category: 'download'
        })
        .get()

      const configs = {}
      result.data.forEach(config => {
        let value = config.value

        switch (config.type) {
          case 'number':
            value = parseInt(value)
            break
          case 'boolean':
            value = value === 'true'
            break
          case 'json':
            value = JSON.parse(value)
            break
        }

        configs[config.key] = value
      })

      return configs
    } catch (error) {
      console.error('获取所有下载配置失败:', error)
      return {}
    }
  }

  // 检查配置是否启用
  async isFeatureEnabled(featureKey) {
    const enabled = await this.getConfig(featureKey, false)
    return enabled === true
  }

  // 获取配置的描述信息
  async getConfigDescription(key) {
    try {
      const db = wx.cloud.database()
      const result = await db.collection('system_configs')
        .where({
          key: key,
          category: 'download'
        })
        .get()

      if (result.data.length > 0) {
        return result.data[0].description
      }

      return '配置项描述'
    } catch (error) {
      console.error('获取配置描述失败:', error)
      return '配置项描述'
    }
  }
}

// 创建全局实例
const downloadConfigManager = new DownloadConfigManager()

module.exports = {
  downloadConfigManager,
  DownloadConfigManager
}
