// 系统配置控制器
const configService = require('../services/configService')
const Joi = require('joi')

class ConfigController {
  // 获取所有配置
  async getAllConfigs(req, res) {
    try {
      const result = await configService.getAllConfigs()
      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 获取单个配置
  async getConfig(req, res) {
    try {
      const { key } = req.params
      const result = await configService.getConfig(key)
      
      if (!result.success) {
        return res.status(404).json(result)
      }
      
      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 更新配置
  async updateConfig(req, res) {
    try {
      const { key } = req.params
      const { value, type = 'string' } = req.body
      
      // 验证输入
      const schema = Joi.object({
        value: Joi.required(),
        type: Joi.string().valid('string', 'number', 'boolean', 'json').default('string')
      })
      
      const { error } = schema.validate({ value, type })
      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message
        })
      }
      
      const result = await configService.updateConfig(key, value, type)
      
      if (!result.success) {
        return res.status(404).json(result)
      }
      
      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 批量更新配置
  async batchUpdateConfigs(req, res) {
    try {
      const { configs } = req.body
      
      // 验证输入
      const schema = Joi.object({
        configs: Joi.array().items(
          Joi.object({
            key: Joi.string().required(),
            value: Joi.required(),
            type: Joi.string().valid('string', 'number', 'boolean', 'json').default('string')
          })
        ).required()
      })
      
      const { error } = schema.validate({ configs })
      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message
        })
      }
      
      const result = await configService.batchUpdateConfigs(configs)
      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 创建配置
  async createConfig(req, res) {
    try {
      const { key, value, type = 'string', category = 'general', description = '' } = req.body
      
      // 验证输入
      const schema = Joi.object({
        key: Joi.string().required(),
        value: Joi.required(),
        type: Joi.string().valid('string', 'number', 'boolean', 'json').default('string'),
        category: Joi.string().default('general'),
        description: Joi.string().default('')
      })
      
      const { error } = schema.validate({ key, value, type, category, description })
      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message
        })
      }
      
      const result = await configService.createConfig({
        key, value, type, category, description
      })
      
      if (!result.success) {
        return res.status(400).json(result)
      }
      
      res.status(201).json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }

  // 删除配置
  async deleteConfig(req, res) {
    try {
      const { key } = req.params
      const result = await configService.deleteConfig(key)
      
      if (!result.success) {
        return res.status(404).json(result)
      }
      
      res.json(result)
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      })
    }
  }
}

module.exports = new ConfigController()
