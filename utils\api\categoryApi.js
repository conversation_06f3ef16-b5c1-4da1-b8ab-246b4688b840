/**
 * 分类页专用API接口
 * 严格按照项目解耦规则：仅供分类页使用，其他页面禁止调用
 */

const db = wx.cloud.database()

/**
 * 获取分类页筛选选项（动态统计）
 */
const getCategoryFilterOptions = async (category = 'regular') => {
  try {
    // 使用聚合查询动态统计可用的筛选选项
    const $ = db.command.aggregate
    
    const result = await db.collection('files')
      .aggregate()
      .match({
        status: 'active',
        category: category
      })
      .group({
        _id: null,
        grades: $.addToSet('$grade'),
        subjects: $.addToSet('$subject'),
        volumes: $.addToSet('$volume'),
        sections: $.addToSet('$section')
      })
      .end()
    
    if (result.list.length > 0) {
      const data = result.list[0]
      
      // 过滤空值并排序
      const grades = data.grades.filter(item => item).sort()
      const subjects = data.subjects.filter(item => item)
      const volumes = data.volumes.filter(item => item)
      const sections = data.sections.filter(item => item).sort()
      
      // 科目按优先级排序
      const subjectOrder = ['语文', '数学', '英语', '科学', '道德与法治']
      subjects.sort((a, b) => {
        const aIndex = subjectOrder.indexOf(a)
        const bIndex = subjectOrder.indexOf(b)
        if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex
        if (aIndex !== -1) return -1
        if (bIndex !== -1) return 1
        return a.localeCompare(b)
      })
      
      // 册别按优先级排序
      const volumeOrder = ['上册', '下册', '全册']
      volumes.sort((a, b) => {
        const aIndex = volumeOrder.indexOf(a)
        const bIndex = volumeOrder.indexOf(b)
        if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex
        if (aIndex !== -1) return -1
        if (bIndex !== -1) return 1
        return a.localeCompare(b)
      })
      
      return {
        success: true,
        data: {
          grades,
          subjects,
          volumes,
          sections
        }
      }
    } else {
      // 返回默认选项
      return {
        success: true,
        data: {
          grades: ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级'],
          subjects: ['语文', '数学', '英语', '科学', '道德与法治'],
          volumes: ['上册', '下册', '全册'],
          sections: ['单元同步', '单元知识点', '期中试卷', '期末试卷', '练习册', '课件资料']
        }
      }
    }
  } catch (error) {
    console.error('获取分类页筛选选项失败:', error)
    return {
      success: false,
      error: error.message,
      data: {
        grades: ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级'],
        subjects: ['语文', '数学', '英语', '科学', '道德与法治'],
        volumes: ['上册', '下册', '全册'],
        sections: ['单元同步', '单元知识点', '期中试卷', '期末试卷', '练习册', '课件资料']
      }
    }
  }
}

/**
 * 获取常规资料文件列表
 */
const getCategoryRegularFiles = async (params = {}) => {
  try {
    // 构建查询条件
    let whereCondition = {
      status: 'active',
      category: 'regular'
    }
    
    // 添加筛选条件
    if (params.grade) whereCondition.grade = params.grade
    if (params.subject) whereCondition.subject = params.subject
    if (params.volume) whereCondition.volume = params.volume
    if (params.section) whereCondition.section = params.section
    
    // 先获取总数
    const countResult = await db.collection('files').where(whereCondition).count()
    const totalCount = countResult.total
    
    let query = db.collection('files').where(whereCondition)
    
    // 排序逻辑
    const sortBy = params.sortBy || 'sort_order'
    const sortOrder = params.sortOrder || 'desc'
    
    if (sortBy === 'download_count') {
      query = query.orderBy('download_count', sortOrder)
        .orderBy('sort_order', 'desc') // 次要排序
    } else if (sortBy === 'created_time') {
      query = query.orderBy('created_time', sortOrder)
        .orderBy('sort_order', 'desc') // 次要排序
    } else {
      query = query.orderBy('sort_order', 'desc')
        .orderBy('created_time', 'desc') // 次要排序
    }
    
    // 分页
    const page = params.page || 1
    const pageSize = params.pageSize || 20
    const skip = (page - 1) * pageSize
    
    query = query.skip(skip).limit(pageSize)
    
    const result = await query.get()
    
    // 计算是否还有更多数据
    const hasMore = skip + result.data.length < totalCount
    
    return {
      success: true,
      data: result.data,
      total: totalCount,
      hasMore: hasMore,
      currentPage: page,
      pageSize: pageSize
    }
    
  } catch (error) {
    console.error('获取分类页常规文件失败:', error)
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
      hasMore: false
    }
  }
}

/**
 * 获取升学专区文件列表
 */
const getCategoryUpgradeFiles = async (params = {}) => {
  try {
    // 构建查询条件
    let whereCondition = {
      status: 'active',
      category: 'upgrade'
    }
    
    // 升学类型筛选
    if (params.upgradeType) {
      whereCondition.grade = params.upgradeType // 幼升小/小升初
    }
    
    // 板块筛选
    if (params.section) {
      whereCondition.section = params.section
    }
    
    // 先获取总数
    const countResult = await db.collection('files').where(whereCondition).count()
    const totalCount = countResult.total
    
    let query = db.collection('files').where(whereCondition)
    
    // 排序逻辑
    const sortBy = params.sortBy || 'sort_order'
    const sortOrder = params.sortOrder || 'desc'
    
    if (sortBy === 'download_count') {
      query = query.orderBy('download_count', sortOrder)
        .orderBy('sort_order', 'desc')
    } else if (sortBy === 'created_time') {
      query = query.orderBy('created_time', sortOrder)
        .orderBy('sort_order', 'desc')
    } else {
      query = query.orderBy('sort_order', 'desc')
        .orderBy('created_time', 'desc')
    }
    
    // 分页
    const page = params.page || 1
    const pageSize = params.pageSize || 20
    const skip = (page - 1) * pageSize
    
    query = query.skip(skip).limit(pageSize)
    
    const result = await query.get()
    
    // 计算是否还有更多数据
    const hasMore = skip + result.data.length < totalCount
    
    return {
      success: true,
      data: result.data,
      total: totalCount,
      hasMore: hasMore,
      currentPage: page,
      pageSize: pageSize
    }
    
  } catch (error) {
    console.error('获取分类页升学文件失败:', error)
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
      hasMore: false
    }
  }
}

/**
 * 获取升学专区筛选选项
 */
const getCategoryUpgradeOptions = async () => {
  try {
    const $ = db.command.aggregate
    
    const result = await db.collection('files')
      .aggregate()
      .match({
        status: 'active',
        category: 'upgrade'
      })
      .group({
        _id: null,
        upgradeTypes: $.addToSet('$grade'),
        sections: $.addToSet('$section')
      })
      .end()
    
    if (result.list.length > 0) {
      const data = result.list[0]
      return {
        success: true,
        data: {
          upgradeTypes: data.upgradeTypes.filter(item => item).sort(),
          sections: data.sections.filter(item => item).sort()
        }
      }
    } else {
      return {
        success: true,
        data: {
          upgradeTypes: ['幼升小', '小升初'],
          sections: [
            '拼音启蒙', '认识数字', '习惯养成', '学科启蒙', '知识科普',
            '语文冲刺', '数学冲刺', '英语强化', '真题模拟', '面试准备'
          ]
        }
      }
    }
  } catch (error) {
    console.error('获取升学专区筛选选项失败:', error)
    return {
      success: false,
      error: error.message,
      data: {
        upgradeTypes: ['幼升小', '小升初'],
        sections: [
          '拼音启蒙', '认识数字', '习惯养成', '学科启蒙', '知识科普',
          '语文冲刺', '数学冲刺', '英语强化', '真题模拟', '面试准备'
        ]
      }
    }
  }
}

module.exports = {
  getCategoryFilterOptions,
  getCategoryRegularFiles,
  getCategoryUpgradeFiles,
  getCategoryUpgradeOptions
}