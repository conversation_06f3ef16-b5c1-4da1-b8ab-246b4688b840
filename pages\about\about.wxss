/* pages/about/about.wxss */
.about-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 应用信息区域 */
.app-info-section {
  background-color: #4285f4;
  padding: 80rpx 40rpx 60rpx;
  text-align: center;
  color: white;
}

.app-logo {
  width: 100rpx;
  height: 100rpx;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30rpx;
}

.logo-text {
  font-size: 50rpx;
}

.app-name {
  font-size: 44rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  line-height: 1.3;
}

.app-version {
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 24rpx;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.app-description {
  font-size: 26rpx;
  opacity: 0.95;
  line-height: 1.6;
  max-width: 500rpx;
  margin: 0 auto;
}

/* 内容区域 */
.features-section,
.services-section {
  background-color: white;
  margin: 30rpx 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 28rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

/* 功能列表 */
.features-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 20rpx 16rpx;
  background-color: #f8f9ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #4285f4;
}

.feature-dot {
  color: #4285f4;
  font-size: 20rpx;
  font-weight: bold;
  margin-right: 16rpx;
  width: 16rpx;
  text-align: center;
}

.feature-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.4;
}

/* 服务列表 */
.services-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.service-item {
  display: flex;
  align-items: center;
  padding: 24rpx 16rpx;
  background-color: #fafafa;
  border-radius: 12rpx;
  border: 1rpx solid #e8e8e8;
  transition: all 0.2s ease;
}

.service-item:active {
  background-color: #f0f0f0;
  transform: scale(0.98);
}

.service-icon {
  font-size: 32rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #4285f4;
  color: white;
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.service-info {
  flex: 1;
}

.service-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.service-desc {
  font-size: 24rpx;
  color: #888;
  line-height: 1.3;
}

.service-arrow {
  font-size: 24rpx;
  color: #ccc;
  font-weight: bold;
}

.service-item:active .service-arrow {
  color: #4285f4;
}

/* 版权信息 */
.copyright-section {
  text-align: center;
  padding: 40rpx 20rpx 60rpx;
  background-color: white;
  margin: 30rpx 20rpx 0;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
}

.copyright-text {
  display: block;
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.copyright-text:last-child {
  margin-bottom: 0;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .app-info-section {
    padding: 60rpx 30rpx 50rpx;
  }
  
  .app-name {
    font-size: 38rpx;
  }
  
  .features-section,
  .services-section,
  .copyright-section {
    margin: 20rpx 15rpx;
    padding: 28rpx;
  }
}

/* 加载状态 */
.loading {
  opacity: 0.7;
}