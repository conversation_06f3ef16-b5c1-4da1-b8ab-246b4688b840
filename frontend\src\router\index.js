import { createRouter, createWebHistory } from 'vue-router'
import ConfigManagement from '@/views/ConfigManagement.vue'

const routes = [
  {
    path: '/',
    redirect: '/stats'
  },
  {
    path: '/config',
    name: 'ConfigManagement',
    component: ConfigManagement
  },
  {
    path: '/files',
    name: 'FileManagement',
    component: () => import('@/views/FileManagement.vue')
  },
  {
    path: '/feedback',
    name: 'FeedbackManagement',
    component: () => import('@/views/FeedbackManagement.vue')
  },
  {
    path: '/stats',
    name: 'DataStats',
    component: () => import('@/views/DataStats.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
