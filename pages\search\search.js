// 搜索页面逻辑
const { searchFiles, getHotKeywords, getSearchSuggestions } = require('../../utils/api/searchApi')
const { 
  processSearchResultData, 
  getSearchSortOptions, 
  getSortDisplayText,
  saveSearchHistory,
  getSearchHistory,
  clearSearchHistory,
  analyzeSearchKeyword,
  formatSearchResultStats,
  isValidSearchKeyword
} = require('../../utils/helpers/searchHelpers')

Page({
  data: {
    // 搜索相关
    searchKeyword: '',
    searchFocus: true,
    searchHistory: [],
    hotKeywords: [],
    searchSuggestions: [],
    showSuggestions: false,

    // 搜索结果
    searchResults: [],
    loading: false,
    loadingMore: false,
    hasMore: true,
    page: 1,
    totalCount: 0,

    // 排序
    sortType: 'sort_order',
    sortOrder: 'desc',
    showSortTool: false,
    sortOptions: [],
    selectedSort: '推荐排序',

    // 输入状态管理
    isTyping: false,
    hasSearched: false,
    showEmptyState: false

  },

  // 防抖定时器
  searchTimer: null,

  onLoad(options) {
    // 从其他页面传入的搜索关键词
    if (options.keyword) {
      this.setData({
        searchKeyword: options.keyword,
        searchFocus: false
      })
      this.performSearch()
    }
    
    // 初始化数据
    this.initializeData()
  },

  onShow() {
    // 页面显示时重新获取焦点
    if (!this.data.searchKeyword) {
      this.setData({ searchFocus: true })
    }
  },

  onHide() {
    // 页面隐藏时清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  },

  onUnload() {
    // 页面卸载时清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loadingMore && this.data.searchKeyword) {
      this.loadMoreResults()
    }
  },

  onPullDownRefresh() {
    if (this.data.searchKeyword) {
      this.performSearch().then(() => {
        wx.stopPullDownRefresh()
      })
    } else {
      wx.stopPullDownRefresh()
    }
  },

  async initializeData() {
    // 加载搜索历史和热门关键词
    this.loadSearchHistory()
    await this.loadHotKeywords()
    
    // 初始化排序选项
    const sortOptions = getSearchSortOptions()
    console.log('初始化排序选项:', sortOptions)
    console.log('当前排序类型:', this.data.sortType)
    console.log('排序显示文本:', getSortDisplayText(this.data.sortType))
    
    this.setData({ 
      sortOptions,
      selectedSort: getSortDisplayText(this.data.sortType)
    })
    
    console.log('排序选项设置完成:', {
      sortOptions: this.data.sortOptions,
      selectedSort: this.data.selectedSort,
      showSortTool: this.data.showSortTool
    })
    

  },

  // 加载搜索历史
  loadSearchHistory() {
    const history = getSearchHistory(10)
    this.setData({ searchHistory: history })
  },

  // 加载热门搜索关键词
  async loadHotKeywords() {
    try {
      const result = await getHotKeywords()
      
      if (result && result.success) {
        this.setData({
          hotKeywords: result.data || []
        })
      }
    } catch (error) {
      console.error('加载热门搜索关键词失败:', error)
    }
  },



  // 搜索输入处理
  onSearchInput(e) {
    const keyword = e.detail.value.trim()

    // 清除之前的定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }

    this.setData({
      searchKeyword: keyword,
      isTyping: true,
      showEmptyState: false
    })

    // 如果输入为空，重置状态
    if (!keyword) {
      this.setData({
        searchSuggestions: [],
        showSuggestions: false,
        searchResults: [],
        totalCount: 0,
        isTyping: false,
        hasSearched: false,
        showEmptyState: false
      })
      return
    }

    // 获取搜索建议（即时响应）
    if (keyword.length >= 2) {
      this.getSuggestions(keyword)
    } else {
      this.setData({
        searchSuggestions: [],
        showSuggestions: false
      })
    }

    // 设置延迟搜索（防抖）
    this.searchTimer = setTimeout(() => {
      this.setData({ isTyping: false })
      if (keyword.length >= 2) {
        this.performAutoSearch(keyword)
      }
    }, 1000) // 1秒延迟
  },

  // 获取搜索建议
  async getSuggestions(keyword) {
    try {
      const result = await getSearchSuggestions(keyword)

      if (result && result.success) {
        this.setData({
          searchSuggestions: result.data,
          showSuggestions: result.data.length > 0
        })
      }
    } catch (error) {
      console.error('获取搜索建议失败:', error)
    }
  },

  // 自动搜索（延迟触发）
  async performAutoSearch(keyword) {
    if (!keyword || keyword.length < 2) return

    // 验证搜索关键词
    if (!isValidSearchKeyword(keyword)) {
      return
    }

    this.setData({
      loading: true,
      page: 1,
      searchResults: [],
      hasMore: true,
      showSuggestions: false,
      hasSearched: true
    })

    try {
      const searchParams = {
        keyword: keyword,
        page: 1,
        pageSize: 20,
        sortBy: this.data.sortType,
        sortOrder: this.data.sortOrder
      }

      const result = await searchFiles(searchParams)

      if (result && result.success) {
        const processedData = processSearchResultData(result.data.list || [])

        this.setData({
          searchResults: processedData,
          hasMore: result.data.pagination.hasMore,
          totalCount: result.data.pagination.total,
          page: 2,
          showEmptyState: processedData.length === 0
        })

        // 保存搜索历史
        const newHistory = saveSearchHistory(keyword)
        this.setData({ searchHistory: newHistory.slice(0, 10) })

      } else {
        this.setData({
          searchResults: [],
          totalCount: 0,
          showEmptyState: true
        })
      }
    } catch (error) {
      console.error('自动搜索失败:', error)
      this.setData({
        searchResults: [],
        totalCount: 0,
        showEmptyState: true
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 执行搜索
  async performSearch() {
    const keyword = this.data.searchKeyword.trim()
    
    // 验证搜索关键词
    if (!isValidSearchKeyword(keyword)) {
      wx.showToast({
        title: keyword ? '搜索关键词格式不正确' : '请输入搜索关键词',
        icon: 'none'
      })
      return
    }

    // 分析搜索关键词
    const keywordAnalysis = analyzeSearchKeyword(keyword)
    console.log('搜索关键词分析:', keywordAnalysis)

    // 清除防抖定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }

    this.setData({
      loading: true,
      page: 1,
      searchResults: [],
      hasMore: true,
      showSuggestions: false,
      searchFocus: false,
      isTyping: false,
      hasSearched: true,
      showEmptyState: false
    })

    try {
      const searchParams = {
        keyword: keyword,
        page: 1,
        pageSize: 20,
        sortBy: this.data.sortType,
        sortOrder: this.data.sortOrder
      }
      
      console.log('搜索API调用参数:', JSON.stringify(searchParams, null, 2))
      
      const result = await searchFiles(searchParams)

      if (result && result.success) {
        const processedData = processSearchResultData(result.data.list || [])

        this.setData({
          searchResults: processedData,
          hasMore: result.data.pagination.hasMore,
          totalCount: result.data.pagination.total,
          page: 2,
          showEmptyState: processedData.length === 0
        })

        // 保存搜索历史
        const newHistory = saveSearchHistory(keyword)
        this.setData({ searchHistory: newHistory.slice(0, 10) })

        // 显示搜索提示
        if (keywordAnalysis.searchTips) {
          console.log('搜索提示:', keywordAnalysis.searchTips)
        }

        // 如果结果较少，给出搜索建议
        if (result.data.pagination.total < 5 && keywordAnalysis.keywords.length > 1) {
          wx.showToast({
            title: `找到${result.data.pagination.total}个结果，已按相关度排序`,
            icon: 'none',
            duration: 2000
          })
        }
      } else {
        this.setData({
          searchResults: [],
          totalCount: 0,
          showEmptyState: true
        })

        // 给出搜索建议
        let suggestionMsg = '未找到相关资料'
        if (keywordAnalysis.keywords.length > 1) {
          suggestionMsg += '，试试减少关键词或调整搜索词'
        } else if (keywordAnalysis.hasGrade && !keywordAnalysis.hasSubject) {
          suggestionMsg += '，试试添加学科名称'
        } else if (keywordAnalysis.hasSubject && !keywordAnalysis.hasGrade) {
          suggestionMsg += '，试试添加年级信息'
        }

        wx.showToast({
          title: suggestionMsg,
          icon: 'none',
          duration: 3000
        })
      }
    } catch (error) {
      console.error('搜索失败:', error)
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      })
      this.setData({
        searchResults: [],
        totalCount: 0,
        showEmptyState: true
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 搜索确认
  onSearch() {
    this.performSearch()
  },

  // 加载更多搜索结果
  async loadMoreResults() {
    this.setData({ loadingMore: true })

    try {
      const searchParams = {
        keyword: this.data.searchKeyword,
        page: this.data.page,
        pageSize: 20,
        sortBy: this.data.sortType,
        sortOrder: this.data.sortOrder
      }
      
      console.log('加载更多API调用参数:', JSON.stringify(searchParams, null, 2))
      
      const result = await searchFiles(searchParams)

      if (result && result.success) {
        const processedData = processSearchResultData(result.data.list || [])

        this.setData({
          searchResults: [...this.data.searchResults, ...processedData],
          hasMore: result.data.pagination.hasMore,
          page: this.data.page + 1
        })
      }
    } catch (error) {
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loadingMore: false })
    }
  },

  // 点击搜索建议
  selectSuggestion(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({
      searchKeyword: keyword,
      showSuggestions: false
    })
    this.performSearch()
  },

  // 点击热门关键词
  selectHotKeyword(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({
      searchKeyword: keyword,
      searchFocus: false
    })
    this.performSearch()
  },

  // 点击搜索历史
  selectHistory(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({
      searchKeyword: keyword,
      searchFocus: false
    })
    this.performSearch()
  },

  // 清除搜索历史
  clearHistory() {
    wx.showModal({
      title: '提示',
      content: '确定要清除所有搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          const success = clearSearchHistory()
          if (success) {
            this.setData({ searchHistory: [] })
            wx.showToast({
              title: '已清除',
              icon: 'success'
            })
          } else {
            wx.showToast({
              title: '清除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 清除搜索内容
  clearSearch() {
    // 清除防抖定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }

    this.setData({
      searchKeyword: '',
      searchResults: [],
      searchFocus: true,
      showSuggestions: false,
      totalCount: 0,
      isTyping: false,
      hasSearched: false,
      showEmptyState: false
    })
  },

  // 显示排序选项
  showSortTool() {
    console.log('显示排序工具')
    console.log('当前排序选项:', this.data.sortOptions)
    console.log('当前排序类型:', this.data.sortType)
    console.log('当前排序顺序:', this.data.sortOrder)
    
    this.setData({ showSortTool: true })
    
    console.log('排序工具显示状态:', this.data.showSortTool)
  },

  // 隐藏排序选项
  hideSortTool() {
    this.setData({ showSortTool: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 选择排序方式
  selectSort(e) {
    const sortType = e.currentTarget.dataset.sort
    
    console.log('选择排序方式:', sortType)
    
    this.setData({
      sortType: sortType,
      selectedSort: getSortDisplayText(sortType),
      showSortTool: false
    })
    
    console.log('当前排序设置:', {
      sortType: this.data.sortType,
      sortOrder: this.data.sortOrder,
      selectedSort: this.data.selectedSort
    })
    
    if (this.data.searchKeyword) {
      this.performSearch()
    }
  },

  // 切换排序顺序
  toggleSortOrder(e) {
    // 阻止事件冒泡，避免触发父级的selectSort
    e.stopPropagation()
    
    const newOrder = this.data.sortOrder === 'desc' ? 'asc' : 'desc'
    console.log('切换排序顺序:', this.data.sortOrder, '->', newOrder)
    
    this.setData({
      sortOrder: newOrder
    })
    
    console.log('排序顺序已更新:', {
      sortType: this.data.sortType,
      sortOrder: this.data.sortOrder
    })
    
    if (this.data.searchKeyword) {
      this.performSearch()
    }
  },

  // 跳转到文件详情
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/files-detail/files-detail?id=${id}`
    })
  }
})