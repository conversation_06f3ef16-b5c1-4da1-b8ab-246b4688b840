// 示例配置数据生成器
const cloudbase = require('@cloudbase/node-sdk')
const path = require('path')
require('dotenv').config({ path: path.join(__dirname, '../../../k12-admin/.env') })

// 示例配置数据
const sampleConfigs = [
  {
    key: 'app_name',
    value: 'K12精品教辅资源',
    type: 'string',
    category: 'general',
    description: '应用名称'
  },
  {
    key: 'app_version',
    value: '1.0.0',
    type: 'string',
    category: 'general',
    description: '应用版本号'
  },
  {
    key: 'max_download_per_day',
    value: '10',
    type: 'number',
    category: 'limits',
    description: '每日最大下载次数'
  },
  {
    key: 'ads_enabled',
    value: 'true',
    type: 'boolean',
    category: 'ads',
    description: '是否启用广告'
  },
  {
    key: 'banner_ad_id',
    value: 'adunit-xxx',
    type: 'string',
    category: 'ads',
    description: '横幅广告ID'
  },
  {
    key: 'reward_ad_id',
    value: 'adunit-yyy',
    type: 'string',
    category: 'ads',
    description: '激励视频广告ID'
  },
  {
    key: 'hot_keywords',
    value: JSON.stringify(['数学', '语文', '英语', '练习册']),
    type: 'json',
    category: 'features',
    description: '热门搜索关键词'
  },
  {
    key: 'file_categories',
    value: JSON.stringify(['练习册', '试卷', '课件', '教案']),
    type: 'json',
    category: 'features',
    description: '文件分类列表'
  }
]

// 生成配置数据
async function generateConfigs() {
  try {
    console.log('🔧 示例配置数据生成器')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    
    const app = cloudbase.init({
      env: process.env.CLOUDBASE_ENV,
      secretId: process.env.CLOUDBASE_SECRET_ID,
      secretKey: process.env.CLOUDBASE_SECRET_KEY
    })
    
    const db = app.database()
    const collection = db.collection('system_configs')
    
    console.log('📊 开始生成示例配置数据...')
    
    for (const config of sampleConfigs) {
      try {
        // 检查是否已存在
        const existing = await collection.where({ key: config.key }).get()
        
        if (existing.data.length > 0) {
          console.log(`⚠️  配置 ${config.key} 已存在，跳过`)
          continue
        }
        
        // 添加时间戳
        const configWithTime = {
          ...config,
          created_time: new Date(),
          updated_time: new Date()
        }
        
        await collection.add({ data: configWithTime })
        console.log(`✅ 配置 ${config.key} 生成成功`)
        
      } catch (error) {
        console.log(`❌ 配置 ${config.key} 生成失败:`, error.message)
      }
    }
    
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('🎉 示例配置数据生成完成')
    
  } catch (error) {
    console.error('❌ 生成失败:', error.message)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  generateConfigs().catch(console.error)
}

module.exports = { sampleConfigs, generateConfigs }
