// 文件详情页面逻辑
const { cloudApi } = require('../../utils/cloudApi')
const { downloadManager } = require('../../utils/downloadManager')
const { downloadConfigManager } = require('../../utils/downloadConfigManager')
const { userManager } = require('../../utils/userManager')
const { DebugHelper } = require('../../utils/debugHelper')

Page({
  data: {
    // 文件详情
    materialInfo: null,
    loading: true,

    // 相关推荐
    relatedMaterials: [],

    // 预览图片
    previewImages: [],

    // 下载状态
    downloading: false,
    downloadProgress: 0,

    // 用户信息
    userOpenId: null,
    userType: null, // 'openid' | 'device' | 'fallback'

    // 下载统计
    downloadStats: {
      todayCount: 0,
      dailyLimit: 10,
      remaining: 10,
      currentDownloads: 0
    },
    

  },

  onLoad(options) {
    const fileId = options.id
    if (fileId) {
      // 初始化页面
      this.initPage(fileId)
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 初始化页面
  async initPage(fileId) {
    try {
      // 1. 初始化用户标识
      await this.initUser()

      // 2. 加载文件详情
      await this.loadFileDetail(fileId)

      // 3. 更新下载统计
      await this.updateDownloadStats()

    } catch (error) {
      console.error('页面初始化失败:', error)
      // 即使用户初始化失败也要加载文件详情
      this.loadFileDetail(fileId)
    }
  },

  // 初始化用户标识
  async initUser() {
    try {
      console.log('🔄 开始初始化用户标识...')
      const userInfo = await userManager.initUser()
      console.log('📋 userManager返回的用户信息:', userInfo)

      this.setData({
        userOpenId: userInfo.userId,
        userType: userInfo.userType
      })

      console.log('✅ 用户标识设置完成:', {
        userOpenId: this.data.userOpenId,
        userType: this.data.userType
      })

      // 显示用户信息
      const displayInfo = userManager.getUserDisplayInfo()
      console.log('用户初始化完成:', displayInfo.display)

    } catch (error) {
      console.error('❌ 用户初始化失败:', error)
      // 设置默认值
      this.setData({
        userOpenId: `fallback_${Date.now()}`,
        userType: 'fallback'
      })
      console.log('⚠️ 使用降级方案:', this.data.userOpenId)
    }
  },

  // 更新下载统计
  async updateDownloadStats() {
    if (!this.data.userOpenId) return

    try {
      const stats = await downloadManager.getDownloadStats(this.data.userOpenId)
      this.setData({
        downloadStats: stats
      })
    } catch (error) {
      console.error('更新下载统计失败:', error)
    }
  },

  // 手动刷新统计
  async refreshStats() {
    await this.updateDownloadStats()
    wx.showToast({
      title: '统计已刷新',
      icon: 'success',
      duration: 1000
    })
  },

  // 测试云函数调用（开发环境使用）
  async testCloudFunction() {
    wx.showLoading({ title: '测试中...' })

    try {
      const testResult = await userManager.testCloudFunction()
      const forceResult = await userManager.forceGetOpenId()

      wx.hideLoading()

      let message = '🧪 云函数测试结果:\n\n'

      // 测试调用结果
      message += `📞 调用状态: ${testResult.success ? '✅ 成功' : '❌ 失败'}\n`
      if (!testResult.success) {
        message += `❌ 错误: ${testResult.error}\n`
      }

      // 云函数返回结果
      if (testResult.cloudFunctionResult) {
        const cfResult = testResult.cloudFunctionResult
        message += `\n📋 云函数返回:\n`
        message += `• 成功: ${cfResult.success ? '✅' : '❌'}\n`
        message += `• 错误: ${cfResult.error || '无'}\n`
        message += `• 消息: ${cfResult.message || '无'}\n`

        if (cfResult.debug) {
          message += `\n🔍 调试信息:\n`
          if (cfResult.debug.context_keys) {
            message += `• 上下文键: ${cfResult.debug.context_keys.join(', ')}\n`
          }
          if (cfResult.debug.source) {
            message += `• 调用来源: ${cfResult.debug.source}\n`
          }
        }
      }

      // 强制获取结果
      message += `\n🔄 强制获取openid:\n`
      message += `• 状态: ${forceResult.success ? '✅ 成功' : '❌ 失败'}\n`
      if (forceResult.success) {
        message += `• OpenID: ${forceResult.openid}\n`
      } else {
        message += `• 错误: ${forceResult.error}\n`
        message += `• 消息: ${forceResult.message}\n`
      }

      wx.showModal({
        title: '云函数测试',
        content: message,
        showCancel: false,
        confirmText: '知道了'
      })

    } catch (error) {
      wx.hideLoading()
      console.error('测试失败:', error)
      wx.showToast({
        title: '测试失败',
        icon: 'none'
      })
    }
  },

  // 系统诊断（开发环境使用）
  async runDiagnosis() {
    try {
      const diagnosis = await DebugHelper.diagnose()

      // 显示详细诊断信息
      let message = '🔍 系统诊断报告\n\n'

      // 环境信息
      message += `📱 平台: ${diagnosis.environment.platform}\n`
      message += `🔧 环境: ${diagnosis.environment.envVersion}\n`
      message += `📦 基础库: ${diagnosis.environment.SDKVersion}\n`
      message += `☁️ 云开发: ${diagnosis.cloudStatus.cloudInit ? '✅' : '❌'}\n`
      message += `🗄️ 数据库: ${diagnosis.cloudStatus.databaseConnected ? '✅' : '❌'}\n`
      message += `⚡ 云函数: ${diagnosis.cloudFunction.functionCallable ? '✅' : '❌'}\n\n`

      // 用户状态
      message += `👤 用户ID: ${this.data.userOpenId}\n`
      message += `🏷️ 用户类型: ${this.data.userType}\n\n`

      // 云函数详情
      if (diagnosis.cloudFunction.result) {
        message += `📋 云函数返回:\n`
        message += `• 成功: ${diagnosis.cloudFunction.result.success ? '✅' : '❌'}\n`
        if (!diagnosis.cloudFunction.result.success) {
          message += `• 错误: ${diagnosis.cloudFunction.result.error}\n`
          message += `• 消息: ${diagnosis.cloudFunction.result.message}\n`
        }
      }

      // 问题和建议
      if (diagnosis.issues.length > 0) {
        message += '\n⚠️ 发现问题:\n'
        diagnosis.issues.forEach(issue => {
          message += `• ${issue}\n`
        })
        message += '\n💡 建议:\n'
        diagnosis.suggestions.forEach(suggestion => {
          message += `• ${suggestion}\n`
        })
      }

      wx.showModal({
        title: '系统诊断',
        content: message,
        showCancel: false,
        confirmText: '知道了'
      })

    } catch (error) {
      console.error('诊断失败:', error)
      wx.showToast({
        title: '诊断失败',
        icon: 'none'
      })
    }
  },



  onShareAppMessage() {
    const detail = this.data.materialInfo
    if (detail) {
      return {
        title: detail.title,
        path: `/pages/files-detail/files-detail?id=${detail._id}`,
        imageUrl: detail.preview_images && detail.preview_images.length > 0 ? detail.preview_images[0].url : ''
      }
    }
    return {}
  },



  // 加载文件详情
  async loadFileDetail(fileId) {
    this.setData({ loading: true })
    
    try {
      const result = await cloudApi.getFileDetail(fileId)
      
      if (result && result.success) {
        const detail = result.data
        
        // 处理预览图片
        const previewImages = detail.preview_images || []
        const previewUrls = previewImages.map(img => img.url || img)
        
        // 格式化材料信息
        const materialInfo = {
          id: detail._id,
          title: detail.title,
          description: detail.description || '暂无描述',
          subject: detail.subject,
          grade: detail.grade,
          volume: detail.volume,
          section: detail.section,
          tags: detail.tags || [],
          pages: detail.pages || 0,
          fileSize: this.formatFileSize(detail.file_size),
          downloadCount: detail.download_count || 0,
          viewCount: detail.view_count || 0,
          shareCount: detail.share_count || 0,
          previewImages: previewUrls,
          adRequiredCount: detail.ad_required_count || 1,
          fileType: (detail.file_type || 'PDF').toUpperCase(),
          fileUrl: detail.file_url
        }
        
        this.setData({
          materialInfo,
          previewImages: previewUrls
        })
        
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: detail.title.length > 10 ? detail.title.substring(0, 10) + '...' : detail.title
        })
        
        // 加载相关推荐
        this.loadRelatedFiles(detail)
        
      } else {
        wx.showToast({
          title: '文件不存在',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
      
    } catch (error) {
      console.error('加载文件详情失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载相关推荐
  async loadRelatedFiles(currentFile) {
    try {
      // 查询相同学科和年级的其他文件作为推荐
      const result = await cloudApi.getFilesList({
        grade: currentFile.grade,
        subject: currentFile.subject,
        pageSize: 6
      })
      
      if (result.success) {
        // 过滤掉当前文件，并格式化数据（参考首页数据格式）
        const relatedMaterials = result.data
          .filter(item => item._id !== currentFile._id)
          .slice(0, 5)
          .map(item => ({
            id: item._id,
            title: item.title,
            tags: Array.isArray(item.tags) ? item.tags.slice(0, 4) : [], // 直接使用数据库tags字段
            features: Array.isArray(item.features) ? item.features : [], // 文件特征
            downloadCount: item.download_count || 0,
            viewCount: item.view_count || 0,
            pages: item.pages || 0,
            size: this.formatFileSize(item.file_size),
            fileType: (item.file_type || 'PDF').toUpperCase()
          }))
        
        this.setData({ relatedMaterials })
      }
      
    } catch (error) {
      console.error('加载相关推荐失败:', error)
    }
  },

  // 格式化文件大小
  formatFileSize(size) {
    if (!size || size <= 0) return '未知'
    if (size > 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
    if (size > 1024) return (size / 1024).toFixed(1) + 'KB'
    return size + 'B'
  },

  // 预览图片
  previewImage(e) {
    const index = e.currentTarget.dataset.index || 0
    const images = this.data.previewImages
    
    if (images.length > 0) {
      wx.previewImage({
        current: images[index],
        urls: images
      })
    }
  },

  // 下载资源（点击下载按钮）
  async downloadResource() {
    console.log('🔽 开始下载资源...')

    const materialInfo = this.data.materialInfo
    console.log('📄 文件信息:', materialInfo)
    console.log('📱 用户ID:', this.data.userOpenId)
    console.log('⬇️ 下载状态:', this.data.downloading)

    if (!materialInfo || this.data.downloading) {
      console.log('❌ 下载条件不满足')
      return
    }

    try {
      // 1. 检查用户登录状态
      if (!this.data.userOpenId) {
        console.log('🔐 用户未登录，开始认证...')
        wx.showToast({
          title: '正在准备下载...',
          icon: 'loading'
        })
        await this.initUser()

        if (!this.data.userOpenId) {
          console.log('❌ 用户认证失败')
          wx.showToast({
            title: '用户认证失败',
            icon: 'none'
          })
          return
        }
        console.log('✅ 用户认证成功:', this.data.userOpenId)
      }

      // 2. 检查下载次数限制
      const stats = this.data.downloadStats
      if (stats.remaining <= 0) {
        wx.showModal({
          title: '下载次数已用完',
          content: `今日下载次数已达上限（${stats.dailyLimit}次），请明天再试。`,
          showCancel: false,
          confirmText: '知道了'
        })
        return
      }

      // 3. 检查本地缓存
      const cachedFile = await this.checkLocalCache(materialInfo.id)
      if (cachedFile) {
        wx.showModal({
          title: '文件已下载',
          content: '该文件已下载到本地，您可以：\n• 重新下载最新版本\n• 直接打开已有文件',
          confirmText: '重新下载',
          cancelText: '打开文件',
          success: (res) => {
            if (res.confirm) {
              // 重新下载前再次检查次数
              if (stats.remaining <= 0) {
                wx.showToast({
                  title: '下载次数已用完',
                  icon: 'none'
                })
                return
              }
              this.startSecureDownload()
            } else {
              this.openDownloadedFile(cachedFile.local_path)
            }
          }
        })
        return
      }

      // 3. 检查下载权限
      console.log('🔒 开始检查下载权限...')
      const permission = await this.checkDownloadPermission(materialInfo.id)
      console.log('🔒 权限检查结果:', permission)

      if (!permission.allowed) {
        console.log('❌ 下载权限被拒绝:', permission.message)
        wx.showToast({
          title: permission.message,
          icon: 'none',
          duration: 2000
        })
        return
      }
      console.log('✅ 下载权限检查通过')

      // 4. 显示下载统计提醒
      const currentStats = this.data.downloadStats
      if (currentStats.remaining <= 3) {
        wx.showModal({
          title: '下载提醒',
          content: `今日还可下载 ${currentStats.remaining} 次，是否继续？`,
          success: (res) => {
            if (res.confirm) {
              this.startSecureDownload()
            }
          }
        })
      } else {
        this.startSecureDownload()
      }

    } catch (error) {
      console.error('下载准备失败:', error)
      wx.showToast({
        title: '下载准备失败，请重试',
        icon: 'none'
      })
    }
  },

  // 检查下载权限
  async checkDownloadPermission(fileId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'checkDownloadPermission',
        data: {
          file_id: fileId
        }
      })

      if (result.result.success) {
        return { allowed: true, message: '允许下载' }
      } else {
        return {
          allowed: false,
          reason: result.result.error,
          message: result.result.message
        }
      }
    } catch (error) {
      console.error('检查下载权限失败:', error)
      return {
        allowed: false,
        reason: 'check_failed',
        message: '权限检查失败，请重试'
      }
    }
  },

  // 检查本地缓存
  async checkLocalCache(fileId) {
    try {
      const records = wx.getStorageSync('download_records') || {}
      const record = records[fileId]

      if (!record) return null

      // 检查文件是否还存在
      const fs = wx.getFileSystemManager()
      try {
        await new Promise((resolve, reject) => {
          fs.stat({
            path: record.local_path,
            success: resolve,
            fail: reject
          })
        })

        // 检查缓存是否过期
        const cacheExpireDays = await downloadConfigManager.getConfig('download_cache_expire_days', 365)
        const now = Date.now()
        const downloadTime = new Date(record.download_time).getTime()
        const expireTime = cacheExpireDays * 24 * 60 * 60 * 1000

        if ((now - downloadTime) < expireTime) {
          return record
        } else {
          // 缓存过期，清理记录
          delete records[fileId]
          wx.setStorageSync('download_records', records)
          return null
        }
      } catch (error) {
        // 文件不存在，清理缓存记录
        delete records[fileId]
        wx.setStorageSync('download_records', records)
        return null
      }
    } catch (error) {
      console.error('检查本地缓存失败:', error)
      return null
    }
  },

  // 开始安全下载
  async startSecureDownload() {
    const materialInfo = this.data.materialInfo
    if (!materialInfo || !materialInfo.fileUrl) {
      wx.showToast({
        title: '文件链接无效',
        icon: 'none'
      })
      return
    }

    this.setData({
      downloading: true,
      downloadProgress: 0
    })

    // 记录下载开始
    const downloadId = downloadManager.recordDownloadStart(materialInfo.id, this.data.userOpenId)

    try {
      // 获取云存储文件的真实下载链接
      const realDownloadUrl = await this.getCloudFileUrl(materialInfo.fileUrl)

      if (!realDownloadUrl) {
        throw new Error('获取下载链接失败')
      }

      // 开始下载
      const downloadTask = wx.downloadFile({
        url: realDownloadUrl,
        success: async (res) => {
          try {
            if (res.statusCode === 200) {
              // 保存文件到本地
              const savedFilePath = await this.saveFileToLocal(res.tempFilePath)

              // 记录下载到云端
              await this.recordDownloadToCloud(materialInfo, savedFilePath)

              // 更新本地缓存
              this.updateLocalCache(materialInfo.id, {
                file_id: materialInfo.id,
                file_title: materialInfo.title,
                file_type: materialInfo.fileType,
                local_path: savedFilePath,
                download_time: new Date(),
                download_count: 1
              })

              // 延迟更新下载统计，确保数据库记录已写入
              setTimeout(async () => {
                await this.updateDownloadStats()
              }, 1000)

              // 提示用户下载完成
              wx.showModal({
                title: '下载完成',
                content: `文件已安全保存到本地，是否立即打开？`,
                confirmText: '打开',
                cancelText: '稍后',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    this.openDownloadedFile(savedFilePath)
                  }
                }
              })

            } else {
              throw new Error(`下载失败，状态码: ${res.statusCode}`)
            }
          } catch (saveError) {
            console.error('保存文件失败:', saveError)
            wx.showToast({
              title: '保存文件失败',
              icon: 'none'
            })
          } finally {
            this.setData({
              downloading: false,
              downloadProgress: 0
            })
            // 记录下载完成
            downloadManager.recordDownloadComplete(downloadId)
          }
        },
        fail: (error) => {
          console.error('下载文件失败:', error)
          wx.showToast({
            title: '下载失败，请检查网络',
            icon: 'none'
          })
          this.setData({
            downloading: false,
            downloadProgress: 0
          })
          // 记录下载完成
          downloadManager.recordDownloadComplete(downloadId)
        }
      })

      // 监听下载进度
      downloadTask.onProgressUpdate((res) => {
        this.setData({
          downloadProgress: res.progress
        })
      })

    } catch (error) {
      console.error('安全下载过程出错:', error)
      wx.showToast({
        title: error.message || '下载失败，请重试',
        icon: 'none'
      })
      this.setData({
        downloading: false,
        downloadProgress: 0
      })
      // 记录下载完成
      downloadManager.recordDownloadComplete(downloadId)
    }
  },

  // 记录下载到云端
  async recordDownloadToCloud(materialInfo, localPath) {
    try {
      const deviceInfo = this.getDeviceInfo()

      const result = await wx.cloud.callFunction({
        name: 'recordDownload',
        data: {
          file_id: materialInfo.id,
          file_info: {
            title: materialInfo.title,
            type: materialInfo.fileType,
            size: materialInfo.fileSize || 0
          },
          local_path: localPath,
          device_info: deviceInfo
        }
      })

      if (result.result.success) {
        // 更新统计信息
        await this.updateDownloadStats()
      } else {
        console.warn('保存下载记录失败:', result.result.message)
      }
    } catch (error) {
      console.error('记录下载到云端失败:', error)
    }
  },

  // 获取设备信息
  getDeviceInfo() {
    try {
      const deviceInfo = wx.getDeviceInfo()
      const appInfo = wx.getAppBaseInfo()
      return {
        platform: deviceInfo.platform,
        version: appInfo.version,
        model: deviceInfo.model
      }
    } catch (error) {
      return {
        platform: 'unknown',
        version: 'unknown',
        model: 'unknown'
      }
    }
  },

  // 更新本地缓存
  updateLocalCache(fileId, recordData) {
    try {
      const records = wx.getStorageSync('download_records') || {}
      records[fileId] = {
        ...recordData,
        cached_time: new Date()
      }
      wx.setStorageSync('download_records', records)
    } catch (error) {
      console.error('更新本地缓存失败:', error)
    }
  },

  // 开始下载（保留原方法作为降级方案）
  async startDownload() {
    const materialInfo = this.data.materialInfo
    if (!materialInfo || !materialInfo.fileUrl) {
      wx.showToast({
        title: '文件链接无效',
        icon: 'none'
      })
      return
    }

    this.setData({
      downloading: true,
      downloadProgress: 0
    })

    try {
      // 获取云存储文件的真实下载链接
      const realDownloadUrl = await this.getCloudFileUrl(materialInfo.fileUrl)

      if (!realDownloadUrl) {
        throw new Error('获取下载链接失败')
      }

      // 开始真实下载
      const downloadTask = wx.downloadFile({
        url: realDownloadUrl,
        success: async (res) => {
          try {
            if (res.statusCode === 200) {
              // 获取文件扩展名
              const fileExtension = this.getFileExtension(materialInfo.fileType)
              const fileName = `${materialInfo.title}.${fileExtension}`

              // 保存文件到本地
              const savedFilePath = await this.saveFileToLocal(res.tempFilePath, fileName)

              // 更新下载次数
              await cloudApi.updateDownloadCount(materialInfo.id)

              // 更新本地显示的下载次数
              this.setData({
                'materialInfo.downloadCount': materialInfo.downloadCount + 1
              })

              // 提示用户下载完成并询问是否打开
              wx.showModal({
                title: '下载完成',
                content: `文件已保存到本地，是否立即打开？`,
                confirmText: '打开',
                cancelText: '稍后',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    this.openDownloadedFile(savedFilePath)
                  }
                }
              })

            } else {
              throw new Error(`下载失败，状态码: ${res.statusCode}`)
            }
          } catch (saveError) {
            console.error('保存文件失败:', saveError)
            wx.showToast({
              title: '保存文件失败',
              icon: 'none'
            })
          } finally {
            this.setData({
              downloading: false,
              downloadProgress: 0
            })
          }
        },
        fail: (error) => {
          console.error('下载文件失败:', error)
          wx.showToast({
            title: '下载失败，请检查网络',
            icon: 'none'
          })
          this.setData({
            downloading: false,
            downloadProgress: 0
          })
        }
      })

      // 监听下载进度
      downloadTask.onProgressUpdate((res) => {
        this.setData({
          downloadProgress: res.progress
        })
      })

    } catch (error) {
      console.error('下载过程出错:', error)
      wx.showToast({
        title: error.message || '下载失败，请重试',
        icon: 'none'
      })
      this.setData({
        downloading: false,
        downloadProgress: 0
      })
    }
  },

  // 获取云存储文件的真实下载URL
  async getCloudFileUrl(cloudPath) {
    console.log('开始获取云存储文件URL:', cloudPath)

    return new Promise((resolve, reject) => {
      wx.cloud.getTempFileURL({
        fileList: [cloudPath],
        success: (res) => {
          console.log('getTempFileURL 响应:', res)

          if (res.fileList && res.fileList.length > 0) {
            const fileInfo = res.fileList[0]
            if (fileInfo.status === 0) {
              console.log('获取下载链接成功:', fileInfo.tempFileURL)
              resolve(fileInfo.tempFileURL)
            } else {
              console.error('获取下载链接失败:', fileInfo.errMsg)
              reject(new Error(fileInfo.errMsg || '文件不存在或已过期'))
            }
          } else {
            reject(new Error('未获取到文件信息'))
          }
        },
        fail: (error) => {
          console.error('获取云存储文件URL失败:', error)
          reject(new Error('获取文件链接失败，请重试'))
        }
      })
    })
  },

  // 获取文件扩展名
  getFileExtension(fileType) {
    const typeMap = {
      'PDF': 'pdf',
      'DOC': 'doc',
      'DOCX': 'docx',
      'XLS': 'xls',
      'XLSX': 'xlsx',
      'PPT': 'ppt',
      'PPTX': 'pptx'
    }
    return typeMap[fileType?.toUpperCase()] || 'pdf'
  },

  // 保存文件到本地
  async saveFileToLocal(tempFilePath) {
    return new Promise((resolve, reject) => {
      // 使用微信的saveFile API保存到本地
      wx.saveFile({
        tempFilePath: tempFilePath,
        success: (res) => {
          console.log('文件保存成功:', res.savedFilePath)
          resolve(res.savedFilePath)
        },
        fail: (error) => {
          console.error('文件保存失败:', error)
          reject(error)
        }
      })
    })
  },

  // 打开下载的文件
  openDownloadedFile(filePath) {
    wx.openDocument({
      filePath: filePath,
      showMenu: true,
      success: () => {
        console.log('文件打开成功')
      },
      fail: (error) => {
        console.error('文件打开失败:', error)
        wx.showToast({
          title: '文件打开失败',
          icon: 'none'
        })
      }
    })
  },

  // 跳转到相关文件详情
  goToRelated(e) {
    const id = e.currentTarget.dataset.id
    wx.redirectTo({
      url: `/pages/files-detail/files-detail?id=${id}`
    })
  }
})
