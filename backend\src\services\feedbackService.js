// 用户反馈服务
const { db } = require('../config/cloudbase')

class FeedbackService {
  constructor() {
    this.db = db
    this.collection = this.db.collection('feedback')
  }

  // 获取反馈列表
  async getFeedbackList(params = {}) {
    try {
      const {
        page = 1,
        pageSize = 20,
        status = '',
        type = '',
        keyword = '',
        startDate = '',
        endDate = '',
        sortBy = 'created_time',
        sortOrder = 'desc'
      } = params

      // 构建查询条件
      let query = {}

      // 状态筛选
      if (status) {
        query.feedback_status = status
      }

      // 类型筛选
      if (type) {
        query.feedback_type = type
      }

      // 关键词搜索（标题和内容）
      if (keyword) {
        query.$or = [
          { feedback_title: { $regex: keyword, $options: 'i' } },
          { feedback_content: { $regex: keyword, $options: 'i' } }
        ]
      }

      // 日期范围筛选
      if (startDate || endDate) {
        query.created_time = {}
        if (startDate) {
          query.created_time.$gte = new Date(startDate)
        }
        if (endDate) {
          query.created_time.$lte = new Date(endDate)
        }
      }

      // 计算总数
      const countResult = await this.collection.where(query).count()
      const total = countResult.total

      // 构建排序
      const orderBy = sortOrder === 'desc' ? 'desc' : 'asc'

      // 分页查询
      const skip = (page - 1) * pageSize
      const result = await this.collection
        .where(query)
        .orderBy(sortBy, orderBy)
        .skip(skip)
        .limit(pageSize)
        .get()

      // 处理数据
      const feedbacks = result.data.map(item => ({
        ...item,
        created_time: item.created_time || item._createTime || new Date(),
        user_email: item.user_email || '',
        feedback_status: item.feedback_status || '待处理'
      }))

      return {
        success: true,
        data: {
          feedbacks,
          total,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(total / pageSize)
        }
      }
    } catch (error) {
      console.error('获取反馈列表失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 获取反馈详情
  async getFeedbackDetail(id) {
    try {
      const result = await this.collection.doc(id).get()

      if (!result.data || result.data.length === 0) {
        return {
          success: false,
          error: '反馈不存在'
        }
      }

      const feedback = result.data[0]
      return {
        success: true,
        data: {
          ...feedback,
          created_time: feedback.created_time || feedback._createTime || new Date(),
          user_email: feedback.user_email || '',
          feedback_status: feedback.feedback_status || '待处理'
        }
      }
    } catch (error) {
      console.error('获取反馈详情失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 更新反馈状态
  async updateFeedbackStatus(id, status) {
    try {
      // 验证状态值
      const validStatuses = ['待处理', '处理中', '已解决', '已关闭']
      if (!validStatuses.includes(status)) {
        return {
          success: false,
          error: '无效的状态值'
        }
      }

      const result = await this.collection.doc(id).update({
        feedback_status: status,
        updated_time: new Date()
      })

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('更新反馈状态失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 批量更新反馈状态
  async batchUpdateStatus(feedbackIds, status) {
    try {
      // 验证状态值
      const validStatuses = ['待处理', '处理中', '已解决', '已关闭']
      if (!validStatuses.includes(status)) {
        return {
          success: false,
          error: '无效的状态值'
        }
      }

      const results = []
      for (const id of feedbackIds) {
        try {
          const result = await this.collection.doc(id).update({
            feedback_status: status,
            updated_time: new Date()
          })
          results.push({ id, success: true, result })
        } catch (error) {
          results.push({ id, success: false, error: error.message })
        }
      }

      const successCount = results.filter(r => r.success).length
      const failCount = results.filter(r => !r.success).length

      return {
        success: true,
        data: {
          total: feedbackIds.length,
          success: successCount,
          failed: failCount,
          results
        }
      }
    } catch (error) {
      console.error('批量更新反馈状态失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 删除反馈
  async deleteFeedback(id) {
    try {
      const result = await this.collection.doc(id).remove()

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('删除反馈失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 批量删除反馈
  async batchDeleteFeedback(feedbackIds) {
    try {
      const results = []
      for (const id of feedbackIds) {
        try {
          const result = await this.collection.doc(id).remove()
          results.push({ id, success: true, result })
        } catch (error) {
          results.push({ id, success: false, error: error.message })
        }
      }

      const successCount = results.filter(r => r.success).length
      const failCount = results.filter(r => !r.success).length

      return {
        success: true,
        data: {
          total: feedbackIds.length,
          success: successCount,
          failed: failCount,
          results
        }
      }
    } catch (error) {
      console.error('批量删除反馈失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 获取反馈统计数据
  async getFeedbackStats() {
    try {
      // 获取总数
      const totalResult = await this.collection.count()
      const total = totalResult.total

      // 按状态统计
      const statusStats = {}
      const statuses = ['待处理', '处理中', '已解决', '已关闭']

      for (const status of statuses) {
        const result = await this.collection.where({
          feedback_status: status
        }).count()
        statusStats[status] = result.total
      }

      // 按类型统计
      const typeStats = {}
      const types = ['功能异常', '功能建议', '内容问题', '其他问题']

      for (const type of types) {
        const result = await this.collection.where({
          feedback_type: type
        }).count()
        typeStats[type] = result.total
      }

      // 今日新增（中国标准时间）
      const now = new Date()
      const chinaOffset = 8 * 60 * 60 * 1000 // 东八区偏移量
      const chinaTime = new Date(now.getTime() + chinaOffset)
      const today = new Date(chinaTime.getFullYear(), chinaTime.getMonth(), chinaTime.getDate())
      const todayResult = await this.collection.where({
        created_time: { $gte: today }
      }).count()

      return {
        success: true,
        data: {
          total,
          statusStats,
          typeStats,
          todayCount: todayResult.total
        }
      }
    } catch (error) {
      console.error('获取反馈统计失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
}

module.exports = new FeedbackService()
