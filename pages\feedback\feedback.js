// 反馈页面逻辑
Page({
  data: {
    // 反馈表单
    feedbackType: 0,
    title: '',
    content: '',
    email: '',

    // 反馈类型选项
    feedbackTypes: [
      { value: 'bug', label: '功能异常' },
      { value: 'suggestion', label: '功能建议' },
      { value: 'content', label: '内容问题' },
      { value: 'other', label: '其他问题' }
    ],

    // 提交状态
    submitting: false
  },

  onLoad() {
    wx.setNavigationBarTitle({ title: '意见反馈' });
    this.setData({
      currentTypeLabel: this.data.feedbackTypes[0].label
    });
  },

  // 选择反馈类型
  onTypeChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      feedbackType: index,
      currentTypeLabel: this.data.feedbackTypes[index].label
    });
  },

  // 输入标题
  onTitleInput(e) {
    this.setData({
      title: e.detail.value
    });
  },

  // 输入反馈内容
  onContentInput(e) {
    this.setData({
      content: e.detail.value
    });
  },

  // 输入邮箱
  onEmailInput(e) {
    this.setData({
      email: e.detail.value
    });
  },

  // 验证邮箱格式
  validateEmail(email) {
    if (!email) return true; // 邮箱是选填的
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // 检查提交频率限制
  checkSubmitLimit() {
    const SUBMIT_INTERVAL = 5 * 60 * 1000; // 5分钟间隔
    const lastSubmitTime = wx.getStorageSync('lastFeedbackTime') || 0;
    const now = Date.now();

    if (lastSubmitTime && (now - lastSubmitTime) < SUBMIT_INTERVAL) {
      const remainingTime = Math.ceil((SUBMIT_INTERVAL - (now - lastSubmitTime)) / 1000 / 60);
      return {
        canSubmit: false,
        remainingMinutes: remainingTime
      };
    }

    return { canSubmit: true };
  },

  // 显示频率限制提示
  showSubmitLimitMessage(remainingMinutes) {
    wx.showModal({
      title: '提交频率限制',
      content: `为了避免重复提交，请等待 ${remainingMinutes} 分钟后再试。感谢您的理解！`,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 提交反馈
  async submitFeedback() {
    // 检查提交频率限制
    const limitCheck = this.checkSubmitLimit();
    if (!limitCheck.canSubmit) {
      this.showSubmitLimitMessage(limitCheck.remainingMinutes);
      return;
    }

    // 表单验证
    if (!this.data.title.trim()) {
      wx.showToast({
        title: '请输入反馈标题',
        icon: 'none'
      });
      return;
    }

    if (this.data.title.trim().length < 5) {
      wx.showToast({
        title: '反馈标题至少5个字符',
        icon: 'none'
      });
      return;
    }

    if (!this.data.content.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      });
      return;
    }

    if (this.data.content.trim().length < 10) {
      wx.showToast({
        title: '反馈内容至少10个字符',
        icon: 'none'
      });
      return;
    }

    // 验证邮箱格式
    if (this.data.email && !this.validateEmail(this.data.email)) {
      wx.showToast({
        title: '请输入正确的邮箱格式',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });

    try {
      // 提交到云数据库
      const db = wx.cloud.database();
      const result = await db.collection('feedback').add({
        data: {
          feedback_type: this.data.feedbackTypes[this.data.feedbackType].label,
          feedback_title: this.data.title.trim(),
          feedback_content: this.data.content.trim(),
          user_email: this.data.email.trim(),
          feedback_status: '待处理',
          created_time: db.serverDate() // ✅ 使用数据库服务器时间
        }
      });

      if (result._id) {
        // 记录提交时间
        wx.setStorageSync('lastFeedbackTime', Date.now());

        wx.showToast({
          title: '提交成功，感谢您的反馈！',
          icon: 'success',
          duration: 2000
        });

        // 清空表单
        this.setData({
          feedbackType: 0,
          title: '',
          content: '',
          email: '',
          currentTypeLabel: this.data.feedbackTypes[0].label
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);

      } else {
        wx.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('提交反馈失败:', error);
      wx.showToast({
        title: '提交失败，请检查网络连接',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 重置表单
  resetForm() {
    wx.showModal({
      title: '提示',
      content: '确定要清空所有内容吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            feedbackType: 0,
            title: '',
            content: '',
            email: '',
            currentTypeLabel: this.data.feedbackTypes[0].label
          });
          wx.showToast({
            title: '已清空',
            icon: 'success'
          });
        }
      }
    });
  }
});
