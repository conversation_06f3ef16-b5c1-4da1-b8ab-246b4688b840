// 记录下载云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { file_id, file_info, local_path, device_info } = event

  try {
    // 验证参数
    if (!file_id || !file_info) {
      return {
        success: false,
        error: 'invalid_params',
        message: '缺少必要参数'
      }
    }

    // 获取用户openid
    const wxContext = cloud.getWXContext()
    const OPENID = wxContext.OPENID

    if (!OPENID) {
      return {
        success: false,
        error: 'no_user_context',
        message: '无法获取用户身份'
      }
    }

    const now = new Date()

    // 每次下载都创建新记录，用于统计限制
    const newRecord = {
      openid: OPENID,
      file_id: file_id,
      file_title: file_info.title || '未知文件',
      file_type: file_info.type || 'unknown',
      file_size: file_info.size || 0,
      download_time: now,
      local_path: local_path || '',
      device_info: device_info || getDefaultDeviceInfo(),
      created_time: now
    }

    const result = await db.collection('download_records').add({
      data: newRecord
    })

    // 同时更新files表的下载次数
    await updateFileDownloadCount(file_id)

    return {
      success: true,
      action: 'created',
      record_id: result._id
    }

  } catch (error) {
    console.error('记录下载失败:', error)
    return {
      success: false,
      error: 'record_failed',
      message: '记录下载失败'
    }
  }
}

// 更新文件下载次数
async function updateFileDownloadCount(fileId) {
  try {
    await db.collection('files')
      .doc(fileId)
      .update({
        data: {
          download_count: db.command.inc(1),
          updated_time: new Date()
        }
      })
  } catch (error) {
    console.error('更新文件下载次数失败:', error)
    // 不影响主流程，只记录错误
  }
}

// 获取默认设备信息
function getDefaultDeviceInfo() {
  return {
    platform: 'unknown',
    version: 'unknown',
    model: 'unknown'
  }
}
