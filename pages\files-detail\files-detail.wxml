<view class="container" wx:if="{{!loading && materialInfo}}">
  <!-- 资料头部信息 -->
  <view class="material-header">
    <!-- 学科图标 -->
    <view class="subject-icon">
      <text class="icon-text">📊</text>
      <text class="subject-name">{{materialInfo.subject || '数学'}}</text>
    </view>
    
    <!-- 标题 -->
    <view class="material-title" bindlongpress="testCloudFunction">{{materialInfo.title}}</view>
    
    <!-- 标签 -->
    <view class="material-tags" wx:if="{{materialInfo.tags && materialInfo.tags.length > 0}}">
      <text class="tag" wx:for="{{materialInfo.tags}}" wx:key="*this">#{{item}}</text>
    </view>
    
    <!-- 统计信息卡片 - 四个部分 -->
    <view class="stats-card">
      <view class="stat-item">
        <view class="stat-icon">📄</view>
        <view class="stat-info">
          <text class="stat-value">{{materialInfo.pages || 0}}</text>
          <text class="stat-label">页数</text>
        </view>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <view class="stat-icon">💾</view>
        <view class="stat-info">
          <text class="stat-value">{{materialInfo.fileSize || '未知'}}</text>
          <text class="stat-label">大小</text>
        </view>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <view class="stat-icon">📥</view>
        <view class="stat-info">
          <text class="stat-value">{{materialInfo.downloadCount}}</text>
          <text class="stat-label">下载</text>
        </view>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <view class="stat-icon">👁</view>
        <view class="stat-info">
          <text class="stat-value">{{materialInfo.viewCount}}</text>
          <text class="stat-label">浏览</text>
        </view>
      </view>
    </view>
    
  </view>

  <!-- 资料描述 -->
  <view class="section-card">
    <view class="section-title">资料介绍</view>
    <view class="description-content">{{materialInfo.description}}</view>
  </view>

  <!-- 资料预览 -->
  <view class="section-card">
    <view class="section-title">📖 资料预览</view>
    <view wx:if="{{materialInfo.previewImages && materialInfo.previewImages.length > 0}}" class="preview-container">
      <view class="preview-grid">
        <view 
          class="preview-item" 
          wx:for="{{materialInfo.previewImages}}" 
          wx:key="*this"
          bindtap="previewImage"
          data-index="{{index}}"
        >
          <image 
            class="preview-image" 
            src="{{item}}" 
            mode="aspectFit"
            lazy-load="{{true}}"
          />
          <view class="preview-overlay">
            <text class="preview-tip">点击查看</text>
          </view>
        </view>
      </view>
      <view class="preview-hint">点击图片可放大查看完整内容</view>
    </view>
    <view wx:else class="preview-placeholder">
      <view class="placeholder-icon">📄</view>
      <view class="placeholder-text">暂无预览图片</view>
      <view class="placeholder-tip">点击下载查看完整内容</view>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="section-card" wx:if="{{relatedMaterials && relatedMaterials.length > 0}}">
    <view class="section-title">📚 相关推荐</view>
    <view class="material-list">
      <view 
        class="material-item" 
        wx:for="{{relatedMaterials}}" 
        wx:key="id"
        bindtap="goToRelated"
        data-id="{{item.id}}"
      >
        <!-- 标题行 -->
        <view class="title-row">
          <view class="material-title">{{item.title}}</view>
        </view>
        
        <!-- 文件信息行：图标 + 页数 + 大小 + 特征 -->
        <view class="file-info-row">
          <image class="file-icon" wx:if="{{item.fileType === 'PDF'}}" src="/images/files/icon_pdf.svg" mode="aspectFit" />
          <image class="file-icon" wx:elif="{{item.fileType === 'DOC' || item.fileType === 'DOCX'}}" src="/images/files/icon_doc.svg" mode="aspectFit" />
          <image class="file-icon" wx:elif="{{item.fileType === 'XLS' || item.fileType === 'XLSX'}}" src="/images/files/icon_xls.svg" mode="aspectFit" />
          <image class="file-icon" wx:elif="{{item.fileType === 'PPT' || item.fileType === 'PPTX'}}" src="/images/files/icon_ppt.svg" mode="aspectFit" />
          <image class="file-icon" wx:else src="/images/files/icon_default.svg" mode="aspectFit" />
          
          <text class="file-pages" wx:if="{{item.pages}}">{{item.pages}}页</text>
          <text class="file-size" wx:if="{{item.size}}">{{item.size}}</text>
          
          <view class="feature-tags-inline">
            <text class="feature-tag-small" wx:for="{{item.features}}" wx:key="*this" wx:for-item="feature">{{feature}}</text>
          </view>
        </view>
        
        <!-- 标签行 -->
        <view class="tags-row" wx:if="{{item.tags && item.tags.length > 0}}">
          <text class="category-tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
        </view>
        
        <!-- 数据积分一行 -->
        <view class="stats-row">
          <view class="material-stats">
            <text class="stat-item">⬇ {{item.downloadCount || 0}}</text>
            <text class="stat-item">👁 {{item.viewCount || 0}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 下载统计信息 -->
  <view class="download-stats-card" wx:if="{{userOpenId}}">
    <view class="stats-row">
      <view class="stat-item-small">
        <text class="stat-label-small">今日下载</text>
        <text class="stat-value-small">{{downloadStats.todayCount}}/{{downloadStats.dailyLimit}}</text>
      </view>
      <view class="stat-item-small">
        <text class="stat-label-small">剩余次数</text>
        <text class="stat-value-small {{downloadStats.remaining <= 3 ? 'warning' : ''}}">{{downloadStats.remaining}}</text>
      </view>
      <view class="stat-item-small" wx:if="{{downloadStats.currentDownloads > 0}}">
        <text class="stat-label-small">下载中</text>
        <text class="stat-value-small">{{downloadStats.currentDownloads}}</text>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 - 全宽下载按钮 -->
  <view class="bottom-actions">
    <!-- 下载按钮 - 充满整行 -->
    <view class="download-btn-full {{downloadStats.remaining <= 0 ? 'disabled' : ''}}" bindtap="downloadResource">
      <view class="download-content" wx:if="{{!downloading}}">
        <text class="download-main">{{downloadStats.remaining <= 0 ? '下载次数已用完' : '立即下载'}}</text>
      </view>
      <!-- 下载进度 -->
      <view class="download-progress" wx:if="{{downloading}}">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{downloadProgress}}%"></view>
        </view>
        <text class="progress-text">{{Math.floor(downloadProgress)}}%</text>
      </view>
    </view>
  </view>
</view>

<!-- 骨架屏加载状态 -->
<view class="skeleton-container" wx:if="{{loading}}">
  <view class="skeleton-header">
    <view class="skeleton-info">
      <view class="skeleton-title"></view>
      <view class="skeleton-tags">
        <view class="skeleton-tag"></view>
        <view class="skeleton-tag"></view>
        <view class="skeleton-tag"></view>
      </view>
      <view class="skeleton-stats">
        <view class="skeleton-stat"></view>
        <view class="skeleton-stat"></view>
        <view class="skeleton-stat"></view>
      </view>
      <view class="skeleton-price"></view>
    </view>
  </view>
  <view class="skeleton-section">
    <view class="skeleton-section-title"></view>
    <view class="skeleton-content"></view>
    <view class="skeleton-content"></view>
  </view>
</view>

<!-- 空状态 -->
<view class="empty" wx:if="{{!loading && !materialInfo}}">
  <text class="empty-icon">📄</text>
  <view class="empty-text">资料不存在</view>
</view>