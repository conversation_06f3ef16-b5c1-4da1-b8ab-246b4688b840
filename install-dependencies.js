// 依赖安装检查和指导脚本
const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🔍 检查K12管理后台预览图功能依赖...\n')

// 检查Node.js依赖
function checkNodeDependencies() {
  console.log('📦 检查Node.js依赖包:')
  
  const dependencies = [
    { name: 'pdf-poppler', required: true, description: 'PDF转图片核心库' },
    { name: 'sharp', required: true, description: '图片处理和压缩库' },
    { name: 'axios', required: false, description: 'HTTP客户端（用于文件下载）' }
  ]

  const results = []

  dependencies.forEach(dep => {
    try {
      require.resolve(dep.name)
      console.log(`  ✅ ${dep.name} - 已安装 (${dep.description})`)
      results.push({ ...dep, installed: true })
    } catch (error) {
      console.log(`  ❌ ${dep.name} - 未安装 (${dep.description})`)
      results.push({ ...dep, installed: false })
    }
  })

  return results
}

// 检查系统依赖
function checkSystemDependencies() {
  console.log('\n🖥️  检查系统依赖:')
  
  const systemDeps = [
    { 
      name: 'poppler-utils', 
      command: 'pdftoppm -h',
      description: 'PDF处理工具集',
      installInstructions: {
        windows: '1. 下载 poppler-utils for Windows\n   2. 解压到 C:\\poppler\n   3. 添加 C:\\poppler\\bin 到系统PATH环境变量',
        linux: 'sudo apt-get install poppler-utils',
        macos: 'brew install poppler'
      }
    }
  ]

  const results = []

  systemDeps.forEach(dep => {
    try {
      execSync(dep.command, { stdio: 'ignore' })
      console.log(`  ✅ ${dep.name} - 已安装 (${dep.description})`)
      results.push({ ...dep, installed: true })
    } catch (error) {
      console.log(`  ❌ ${dep.name} - 未安装 (${dep.description})`)
      results.push({ ...dep, installed: false })
    }
  })

  return results
}

// 生成安装指南
function generateInstallGuide(nodeDeps, systemDeps) {
  console.log('\n📋 安装指南:')
  
  const missingNodeDeps = nodeDeps.filter(dep => !dep.installed && dep.required)
  const missingSystemDeps = systemDeps.filter(dep => !dep.installed)

  if (missingNodeDeps.length === 0 && missingSystemDeps.length === 0) {
    console.log('🎉 所有依赖都已正确安装！预览图功能可以正常使用。')
    return
  }

  if (missingNodeDeps.length > 0) {
    console.log('\n1️⃣ 安装Node.js依赖包:')
    console.log('   在项目根目录执行:')
    console.log(`   npm install ${missingNodeDeps.map(dep => dep.name).join(' ')}`)
    console.log('\n   或者分别安装:')
    missingNodeDeps.forEach(dep => {
      console.log(`   npm install ${dep.name}`)
    })
  }

  if (missingSystemDeps.length > 0) {
    console.log('\n2️⃣ 安装系统依赖:')
    
    const platform = process.platform
    let platformName = 'linux'
    if (platform === 'win32') platformName = 'windows'
    if (platform === 'darwin') platformName = 'macos'

    missingSystemDeps.forEach(dep => {
      console.log(`\n   ${dep.name}:`)
      console.log(`   ${dep.installInstructions[platformName]}`)
    })
  }

  console.log('\n⚠️  注意事项:')
  console.log('   - 安装完成后需要重启终端/命令行')
  console.log('   - Windows用户安装poppler-utils后需要重启应用')
  console.log('   - 确保PATH环境变量配置正确')
}

// 测试预览图功能
async function testPreviewFunction() {
  console.log('\n🧪 测试预览图功能:')
  
  try {
    const previewService = require('./backend/src/services/previewService')
    const deps = previewService.checkDependencies()
    
    console.log('   依赖检查结果:')
    Object.entries(deps).forEach(([name, status]) => {
      console.log(`   ${status ? '✅' : '❌'} ${name}`)
    })

    if (Object.values(deps).every(status => status)) {
      console.log('   🎉 预览图功能测试通过！')
    } else {
      console.log('   ⚠️  部分依赖缺失，预览图功能可能无法正常工作')
    }
  } catch (error) {
    console.log('   ❌ 预览图服务加载失败:', error.message)
  }
}

// 主函数
async function main() {
  try {
    const nodeDeps = checkNodeDependencies()
    const systemDeps = checkSystemDependencies()
    
    generateInstallGuide(nodeDeps, systemDeps)
    
    await testPreviewFunction()
    
    console.log('\n📚 更多帮助:')
    console.log('   - 查看项目文档: README.md')
    console.log('   - 预览图问题排查: 访问 /api/files/storage/config')
    console.log('   - 批量检查预览图: 访问 /api/files/preview-images/check')
    
  } catch (error) {
    console.error('❌ 检查过程中出现错误:', error.message)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  checkNodeDependencies,
  checkSystemDependencies,
  generateInstallGuide,
  testPreviewFunction
}