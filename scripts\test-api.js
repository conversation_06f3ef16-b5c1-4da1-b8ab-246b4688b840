// API测试脚本
// 用于测试文件管理API的新参数

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// 测试文件列表API
async function testFileListAPI() {
  console.log('🧪 测试文件列表API...');
  
  try {
    // 测试1: 基础请求
    console.log('\n1. 测试基础请求...');
    const response1 = await axios.get(`${BASE_URL}/api/files`, {
      params: {
        page: 1,
        pageSize: 10,
        status: 'active'
      }
    });
    console.log('✅ 基础请求成功:', response1.data.success);

    // 测试2: 带时间筛选的请求
    console.log('\n2. 测试时间筛选请求...');
    const today = new Date().toISOString().split('T')[0];
    const response2 = await axios.get(`${BASE_URL}/api/files`, {
      params: {
        page: 1,
        pageSize: 10,
        status: 'active',
        timeRange: 'today',
        startDate: today,
        endDate: today
      }
    });
    console.log('✅ 时间筛选请求成功:', response2.data.success);

    // 测试3: 完整参数请求
    console.log('\n3. 测试完整参数请求...');
    const response3 = await axios.get(`${BASE_URL}/api/files`, {
      params: {
        page: 1,
        pageSize: 10,
        grade: '一年级',
        subject: '数学',
        volume: '上册',
        section: '单元同步',
        status: 'active',
        keyword: '练习',
        timeRange: 'last7days',
        startDate: '2024-01-01',
        endDate: today,
        sortBy: 'created_time',
        sortOrder: 'desc'
      }
    });
    console.log('✅ 完整参数请求成功:', response3.data.success);

    console.log('\n🎉 所有API测试通过！');
    
  } catch (error) {
    console.error('❌ API测试失败:', error.response?.data || error.message);
    process.exit(1);
  }
}

// 主函数
async function main() {
  console.log('========================================');
  console.log('K12-Admin API 测试工具');
  console.log('========================================');
  
  // 检查后端服务是否启动
  try {
    await axios.get(`${BASE_URL}/api/health`);
    console.log('✅ 后端服务已启动');
  } catch (error) {
    console.log('❌ 后端服务未启动，请先启动后端服务');
    console.log('启动命令: cd k12-admin/backend && npm run dev');
    process.exit(1);
  }

  await testFileListAPI();
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testFileListAPI };
