# 开发环境配置
VITE_NODE_ENV=development

# API配置
VITE_API_BASE_URL=http://localhost:3000
VITE_API_TIMEOUT=10000

# 应用配置
VITE_APP_TITLE=K12教育资源管理后台
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=K12教育资源管理后台系统

# 文件上传配置
VITE_UPLOAD_MAX_SIZE=52428800
VITE_UPLOAD_CHUNK_SIZE=1048576
VITE_UPLOAD_CONCURRENT=3

# 分页配置
VITE_PAGE_SIZE=20
VITE_PAGE_SIZE_OPTIONS=10,20,50,100

# 缓存配置
VITE_CACHE_EXPIRE=3600000

# 调试配置
VITE_DEBUG=true
VITE_MOCK_API=false

# 主题配置
VITE_THEME_COLOR=#409EFF
VITE_THEME_MODE=light

# 功能开关
VITE_ENABLE_UPLOAD=true
VITE_ENABLE_BATCH_OPERATION=true
VITE_ENABLE_EXPORT=true
VITE_ENABLE_STATISTICS=true
