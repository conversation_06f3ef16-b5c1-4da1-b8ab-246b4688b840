# K12 Admin 部署指南

## 📦 给同事的完整部署包

### 🎯 目标
让同事能够**零配置、开箱即用**地运行 K12 Admin 系统，确保端口一致性和环境统一。

## 🚀 快速部署（推荐流程）

### 步骤 1: 获取项目
```bash
# 方式一：从代码仓库克隆
git clone [项目地址]
cd k12-admin

# 方式二：解压项目压缩包
# 解压 k12-admin.zip 到任意目录
```

### 步骤 2: 环境检查
```bash
# 检查环境是否满足要求
npm run check
```

### 步骤 3: 一键启动
```bash
# 方式一：命令行启动
npm run dev

# 方式二：Windows 双击启动
# 双击 run.bat 文件
```

### 步骤 4: 访问系统
- 前端地址：http://localhost:8080
- 后端API：http://localhost:8081

## 📋 系统要求清单

### 必需软件
- [x] **Node.js 16.0.0+** - [下载地址](https://nodejs.org/)
- [x] **npm** (随 Node.js 安装)
- [x] **现代浏览器** (Chrome/Firefox/Safari/Edge)

### 系统兼容性
- [x] Windows 10/11 (主要支持)
- [x] Windows Server 2019/2022

## 🔧 详细部署步骤

### 1. Node.js 安装验证
```bash
# 检查版本
node --version  # 应显示 v16.0.0 或更高
npm --version   # 应显示 8.0.0 或更高

# 如果版本过低，请重新安装 Node.js
```

### 2. 项目文件检查
确保项目包含以下关键文件：
```
k12-admin/
├── ✅ package.json          # 根项目配置
├── ✅ .env                  # 环境变量（已预配置）
├── ✅ start.js             # 智能启动脚本
├── ✅ run.bat              # Windows 一键启动
├── ✅ check-env.js         # 环境检查脚本
├── ✅ README.md            # 使用说明
├── ✅ DEPLOYMENT.md        # 部署指南
├── 📁 frontend/            # 前端项目
│   ├── ✅ package.json
│   ├── ✅ vite.config.js
│   └── 📁 src/
└── 📁 backend/             # 后端项目
    ├── ✅ package.json
    └── 📁 src/
```

### 3. 依赖安装
```bash
# 自动安装所有依赖
npm run install:all

# 或者手动分步安装
npm install                    # 安装根项目依赖
cd backend && npm install      # 安装后端依赖
cd ../frontend && npm install  # 安装前端依赖
```

### 4. 环境配置验证
检查 `.env` 文件配置：
```env
# 端口配置（固定，请勿修改）
PORT=8081
CORS_ORIGIN=http://localhost:8080

# 云开发配置（已预配置）
CLOUDBASE_ENV=cloud1-8gm001v7fd56ff43
CLOUDBASE_SECRET_ID=AKIDb8UeZN0V1PcirumRuBGBtTAh2AD6xwxu
CLOUDBASE_SECRET_KEY=qVzGKCtOhnhOs9fy5zmOmy1ueGjz3ubY

# 文件上传配置
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=pdf,doc,docx,ppt,pptx
```

## 🎛️ 启动选项

### 选项 1: Windows 一键启动（推荐）
```bash
# 双击 run.bat 文件
# 或在命令行中运行：
run.bat
```

### 选项 2: 命令行启动
```cmd
npm run dev
# 或
npm start
```

### 选项 3: 完整环境设置（首次使用）
```cmd
# 双击 setup.bat 文件
# 或在命令行中运行：
setup.bat
```

### 选项 4: 手动启动（调试用）
```cmd
# 命令提示符 1：启动后端
cd backend
npm run dev

# 命令提示符 2：启动前端
cd frontend
npm run dev
```

## 🔍 故障排除

### 问题 1: Node.js 版本过低
```bash
# 症状：启动时报错 "Node.js version too old"
# 解决：升级 Node.js
# 1. 访问 https://nodejs.org/
# 2. 下载 LTS 版本（推荐 18.x）
# 3. 安装后重启命令行
```

### 问题 2: 端口被占用
```bash
# 症状：启动时提示端口 8080 或 8081 被占用
# 解决：启动脚本会自动处理，如仍有问题：

# Windows 命令：
netstat -ano | findstr :8080
taskkill /F /PID [进程ID]

# 或者重启电脑（简单粗暴）
```

### 问题 3: 依赖安装失败
```bash
# 症状：npm install 报错
# 解决方案：

# 1. 清理缓存
npm cache clean --force

# 2. 删除 node_modules 重新安装
rm -rf node_modules package-lock.json
npm install

# 3. 使用淘宝镜像（中国用户）
npm config set registry https://registry.npmmirror.com
npm run install:all
```

### 问题 4: 文件上传失败
```bash
# 症状：上传文件时报错
# 检查项：
# 1. 文件格式是否支持（PDF、DOC、DOCX、PPT、PPTX）
# 2. 文件大小是否超过 50MB
# 3. 网络连接是否正常
# 4. 云开发服务是否可访问
```

## 📊 端口配置说明

### 固定端口设计
为确保团队协作一致性，系统使用固定端口：

| 服务 | 端口 | 地址 | 说明 |
|------|------|------|------|
| 前端 | 8080 | http://localhost:8080 | 用户访问入口 |
| 后端 | 8081 | http://localhost:8081 | API 服务 |

### 端口修改（不推荐）
如必须修改端口，需同时更新：
1. `.env` 文件中的 `PORT` 和 `CORS_ORIGIN`
2. `frontend/vite.config.js` 中的代理配置
3. `start.js` 中的端口常量

## 🚀 生产环境部署

### 构建生产版本
```bash
# 构建前端
cd frontend
npm run build

# 后端生产配置
cd ../backend
# 修改 .env 中的 NODE_ENV=production
```

### 服务器部署
```bash
# 使用 PM2 管理进程
npm install -g pm2
pm2 start start.js --name k12-admin
pm2 startup
pm2 save
```

## 📞 技术支持

### 联系方式
- 开发团队：[联系方式]
- 技术文档：README.md
- 问题反馈：[问题追踪地址]

### 常用命令
```bash
npm run check      # 环境检查
npm run dev        # 开发模式启动
npm run setup      # 完整环境设置
npm run install:all # 安装所有依赖
```

---

**部署成功后，请访问 http://localhost:8080 开始使用 K12 Admin 系统！**
