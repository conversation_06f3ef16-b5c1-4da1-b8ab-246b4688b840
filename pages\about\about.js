// 关于页面逻辑
Page({
  data: {
    // 应用信息
    appInfo: {
      name: '小学精品教辅资料-原创免费下载',
      version: '1.0.0',
      description: '专门为幼升小到六年级的家长提供各类教辅资料的查找和下载服务',
      features: [
        '海量优质学习资料',
        '智能分类筛选',
        '便捷搜索功能',
        '升学专区指导',
        '精品免费下载'
      ]
    },
    
    // 加载状态
    loading: true
  },

  onLoad() {
    wx.setNavigationBarTitle({ title: '关于我们' });
    this.loadSystemConfigs();
  },

  // 从云数据库加载系统配置
  async loadSystemConfigs() {
    try {
      const db = wx.cloud.database();
      const result = await db.collection('system_configs')
        .where({
          category: 'general'
        })
        .get();

      if (result.data && result.data.length > 0) {
        const configs = {};
        result.data.forEach(item => {
          configs[item.key] = item.value;
        });

        // 更新应用信息，处理换行符
        const appName = configs.app_name || this.data.appInfo.name;
        // 将 - 替换为换行符，或者处理 \n 换行符
        const formattedName = appName.replace(/-/g, '\n').replace(/\\n/g, '\n');
        
        this.setData({
          'appInfo.name': formattedName,
          'appInfo.version': configs.app_version || this.data.appInfo.version,
          loading: false
        });
      } else {
        this.setData({ loading: false });
      }
    } catch (error) {
      console.error('加载系统配置失败:', error);
      wx.showToast({
        title: '配置加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },


  // 跳转到反馈页面
  goToFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  },

  // 分享应用
  onShareAppMessage() {
    return {
      title: '小学精品教辅免费下载',
      path: '/pages/index/index'
    };
  },

  // 返回首页
  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
});