const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// 配置端口
const BACKEND_PORT = 8081;  // 与前端代理配置保持一致
const FRONTEND_PORT = 8080;

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查端口是否被占用并杀死进程
function killProcessOnPort(port) {
  return new Promise((resolve) => {
    // Windows命令查找占用端口的进程
    exec(`netstat -ano | findstr :${port}`, (error, stdout) => {
      if (error || !stdout) {
        log(`端口 ${port} 未被占用`, 'green');
        resolve();
        return;
      }

      // 解析PID
      const lines = stdout.trim().split('\n');
      const pids = new Set();

      lines.forEach(line => {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 5) {
          const pid = parts[parts.length - 1];
          if (pid && pid !== '0') {
            pids.add(pid);
          }
        }
      });

      if (pids.size === 0) {
        log(`端口 ${port} 未被占用`, 'green');
        resolve();
        return;
      }

      log(`发现端口 ${port} 被以下进程占用: ${Array.from(pids).join(', ')}`, 'yellow');

      // 杀死所有占用端口的进程
      let killedCount = 0;
      pids.forEach(pid => {
        exec(`taskkill /F /PID ${pid}`, (killError) => {
          killedCount++;
          if (killError) {
            log(`杀死进程 ${pid} 失败: ${killError.message}`, 'red');
          } else {
            log(`成功杀死进程 ${pid}`, 'green');
          }

          if (killedCount === pids.size) {
            // 等待一秒确保端口释放
            setTimeout(resolve, 1000);
          }
        });
      });
    });
  });
}

// 检查依赖是否安装
function checkDependencies() {
  const backendNodeModules = path.join(__dirname, 'backend', 'node_modules');
  const frontendNodeModules = path.join(__dirname, 'frontend', 'node_modules');

  if (!fs.existsSync(backendNodeModules) || !fs.existsSync(frontendNodeModules)) {
    log('检测到依赖未安装，正在安装依赖...', 'yellow');
    return new Promise((resolve, reject) => {
      const installProcess = spawn('npm', ['run', 'install:all'], {
        cwd: __dirname,
        stdio: 'inherit',
        shell: true
      });

      installProcess.on('close', (code) => {
        if (code === 0) {
          log('依赖安装完成', 'green');
          resolve();
        } else {
          log('依赖安装失败', 'red');
          reject(new Error('依赖安装失败'));
        }
      });
    });
  }

  return Promise.resolve();
}

// 启动后端服务
function startBackend() {
  return new Promise((resolve) => {
    log('正在启动后端服务...', 'blue');

    const backend = spawn('npm', ['run', 'dev'], {
      cwd: path.join(__dirname, 'backend'),
      stdio: 'pipe',
      shell: true
    });

    backend.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`[后端] ${output.trim()}`);

      // 检查是否启动成功
      if (output.includes('服务器运行在') || output.includes('Server running') || output.includes(`${BACKEND_PORT}`)) {
        log('后端服务启动成功!', 'green');
        resolve(backend);
      }
    });

    backend.stderr.on('data', (data) => {
      console.error(`[后端错误] ${data.toString().trim()}`);
    });

    backend.on('close', (code) => {
      log(`后端服务退出，代码: ${code}`, 'red');
    });

    // 5秒后如果没有成功消息，也认为启动成功
    setTimeout(() => {
      log('后端服务启动中...', 'yellow');
      resolve(backend);
    }, 5000);
  });
}

// 启动前端服务
function startFrontend() {
  return new Promise((resolve) => {
    log('正在启动前端服务...', 'blue');

    const frontend = spawn('npm', ['run', 'dev'], {
      cwd: path.join(__dirname, 'frontend'),
      stdio: 'pipe',
      shell: true
    });

    frontend.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`[前端] ${output.trim()}`);

      // 检查是否启动成功
      if (output.includes('Local:') || output.includes('localhost') || output.includes(`${FRONTEND_PORT}`)) {
        log('前端服务启动成功!', 'green');
        resolve(frontend);
      }
    });

    frontend.stderr.on('data', (data) => {
      console.error(`[前端错误] ${data.toString().trim()}`);
    });

    frontend.on('close', (code) => {
      log(`前端服务退出，代码: ${code}`, 'red');
    });

    // 10秒后如果没有成功消息，也认为启动成功
    setTimeout(() => {
      log('前端服务启动中...', 'yellow');
      resolve(frontend);
    }, 10000);
  });
}

// 主启动函数
async function start() {
  try {
    log('=== K12 Admin 一键启动脚本 ===', 'cyan');
    log('正在检查并清理端口占用...', 'yellow');

    // 清理端口占用
    await killProcessOnPort(BACKEND_PORT);
    await killProcessOnPort(FRONTEND_PORT);

    // 检查依赖
    await checkDependencies();

    // 启动服务
    log('正在启动服务...', 'yellow');
    const backendProcess = await startBackend();

    // 等待2秒再启动前端，确保后端先启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    const frontendProcess = await startFrontend();

    log('=== 服务启动完成 ===', 'green');
    log(`后端服务: http://localhost:${BACKEND_PORT}`, 'cyan');
    log(`前端服务: http://localhost:${FRONTEND_PORT}`, 'cyan');
    log('按 Ctrl+C 停止所有服务', 'yellow');

    // 处理退出信号
    process.on('SIGINT', () => {
      log('\n正在停止服务...', 'yellow');
      backendProcess.kill();
      frontendProcess.kill();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      log('\n正在停止服务...', 'yellow');
      backendProcess.kill();
      frontendProcess.kill();
      process.exit(0);
    });

  } catch (error) {
    log(`启动失败: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 启动应用
start();
