// 云开发连接测试脚本
const cloudbase = require('@cloudbase/node-sdk')
const path = require('path')
require('dotenv').config({ path: path.join(__dirname, '../../../k12-admin/.env') })

console.log('🔧 云开发连接测试工具')
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')

// 验证环境变量
function validateEnv() {
  const required = ['CLOUDBASE_ENV', 'CLOUDBASE_SECRET_ID', 'CLOUDBASE_SECRET_KEY']
  const missing = required.filter(key => !process.env[key])
  
  if (missing.length > 0) {
    console.error('❌ 缺少环境变量:', missing.join(', '))
    return false
  }
  
  console.log('✅ 环境变量验证通过')
  return true
}

// 测试云开发连接
async function testConnection() {
  try {
    const app = cloudbase.init({
      env: process.env.CLOUDBASE_ENV,
      secretId: process.env.CLOUDBASE_SECRET_ID,
      secretKey: process.env.CLOUDBASE_SECRET_KEY
    })
    
    const db = app.database()
    
    // 测试数据库连接
    console.log('📊 测试数据库连接...')
    const collections = ['system_configs', 'files', 'feedback']
    
    for (const collection of collections) {
      try {
        const result = await db.collection(collection).limit(1).get()
        console.log(`✅ ${collection} 集合连接成功 (${result.data.length} 条记录)`)
      } catch (error) {
        console.log(`❌ ${collection} 集合连接失败:`, error.message)
      }
    }
    
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('🎉 云开发连接测试完成')
    
  } catch (error) {
    console.error('❌ 云开发初始化失败:', error.message)
  }
}

// 主函数
async function main() {
  if (validateEnv()) {
    await testConnection()
  }
}

main().catch(console.error)
