<template>
  <div class="file-stats-panel">
    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-overview">
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic
            title="文件总数"
            :value="stats.total || 0"
            :precision="0"
          >
            <template #prefix>
              <el-icon style="color: #409eff"><Document /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic
            title="总下载量"
            :value="totalDownloads"
            :precision="0"
          >
            <template #prefix>
              <el-icon style="color: #67c23a"><Download /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic
            title="总查看量"
            :value="totalViews"
            :precision="0"
          >
            <template #prefix>
              <el-icon style="color: #e6a23c"><View /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic
            :title="storageTitle"
            :value="totalStorage"
            suffix="MB"
            :precision="2"
          >
            <template #prefix>
              <el-icon style="color: #f56c6c"><FolderOpened /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分类统计 -->
    <el-row :gutter="20" class="category-stats">
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>按分类统计</span>
            </div>
          </template>
          <div class="chart-container">
            <div v-if="stats.categoryStats && stats.categoryStats.length > 0" class="stats-list">
              <div
                v-for="item in stats.categoryStats"
                :key="item._id"
                class="stats-item"
              >
                <div class="stats-label">
                  {{ getCategoryName(item._id) }}
                </div>
                <div class="stats-value">{{ item.count }}</div>
                <div class="stats-bar">
                  <div
                    class="stats-bar-fill"
                    :style="{ width: getPercentage(item.count, stats.total) + '%' }"
                  ></div>
                </div>
              </div>
            </div>
            <el-empty v-else description="暂无数据" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>按年级统计</span>
            </div>
          </template>
          <div class="chart-container">
            <div v-if="stats.gradeStats && stats.gradeStats.length > 0" class="stats-list">
              <div
                v-for="item in stats.gradeStats"
                :key="item._id"
                class="stats-item"
              >
                <div class="stats-label">{{ item._id || '未分类' }}</div>
                <div class="stats-value">{{ item.count }}</div>
                <div class="stats-bar">
                  <div
                    class="stats-bar-fill grade-bar"
                    :style="{ width: getPercentage(item.count, stats.total) + '%' }"
                  ></div>
                </div>
              </div>
            </div>
            <el-empty v-else description="暂无数据" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>按科目统计</span>
            </div>
          </template>
          <div class="chart-container">
            <div v-if="stats.subjectStats && stats.subjectStats.length > 0" class="stats-list">
              <div
                v-for="item in stats.subjectStats"
                :key="item._id"
                class="stats-item"
              >
                <div class="stats-label">{{ item._id }}</div>
                <div class="stats-value">{{ item.count }}</div>
                <div class="stats-bar">
                  <div
                    class="stats-bar-fill subject-bar"
                    :style="{ width: getPercentage(item.count, stats.total) + '%' }"
                  ></div>
                </div>
              </div>
            </div>
            <el-empty v-else description="暂无数据" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 下载排行榜 -->
    <el-row class="ranking-section">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>下载量排行榜</span>
              <el-tag type="info" size="small">TOP 10</el-tag>
            </div>
          </template>
          <div class="ranking-container">
            <el-table
              v-if="stats.downloadRanking && stats.downloadRanking.length > 0"
              :data="stats.downloadRanking"
              stripe
              style="width: 100%"
            >
              <el-table-column type="index" label="排名" width="80">
                <template #default="{ $index }">
                  <div class="ranking-index">
                    <el-icon v-if="$index === 0" style="color: #ffd700"><Trophy /></el-icon>
                    <el-icon v-else-if="$index === 1" style="color: #c0c0c0"><Medal /></el-icon>
                    <el-icon v-else-if="$index === 2" style="color: #cd7f32"><Medal /></el-icon>
                    <span v-else>{{ $index + 1 }}</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="title" label="文件标题" min-width="300">
                <template #default="{ row }">
                  <div class="file-title">{{ row.title }}</div>
                </template>
              </el-table-column>

              <el-table-column prop="grade" label="年级" width="100" />
              <el-table-column prop="subject" label="科目" width="100" />

              <el-table-column prop="download_count" label="下载量" width="120">
                <template #default="{ row }">
                  <el-tag type="success">{{ row.download_count }}</el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="view_count" label="查看量" width="120">
                <template #default="{ row }">
                  <el-tag type="info">{{ row.view_count }}</el-tag>
                </template>
              </el-table-column>
            </el-table>
            <el-empty v-else description="暂无数据" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Document, Download, View, FolderOpened, Trophy, Medal } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  stats: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 计算属性
const totalDownloads = computed(() => {
  if (!props.stats.downloadRanking) return 0
  return props.stats.downloadRanking.reduce((total, item) => total + (item.download_count || 0), 0)
})

const totalViews = computed(() => {
  if (!props.stats.downloadRanking) return 0
  return props.stats.downloadRanking.reduce((total, item) => total + (item.view_count || 0), 0)
})

const totalStorage = computed(() => {
  // 如果后端提供了实际存储空间数据，使用真实数据
  if (props.stats.totalStorage) {
    return props.stats.totalStorage
  }

  // 否则基于文件数量进行合理估算
  const fileCount = props.stats.total || 0

  // 根据文件类型进行更精确的估算
  let avgSize = 2.0 // 默认平均大小 2MB

  if (props.stats.fileTypeStats && props.stats.fileTypeStats.length > 0) {
    // 根据文件类型分布调整平均大小
    const typeWeights = {
      'pdf': 3.5,    // PDF文件通常较大
      'doc': 1.5,    // Word文档中等
      'docx': 1.8,   // 新版Word文档
      'ppt': 4.0,    // PPT文件较大
      'pptx': 4.5,   // 新版PPT文件
      'xls': 1.2,    // Excel文件较小
      'xlsx': 1.5    // 新版Excel文件
    }

    let totalWeight = 0
    let totalFiles = 0

    props.stats.fileTypeStats.forEach(item => {
      const weight = typeWeights[item._id] || 2.0
      totalWeight += weight * item.count
      totalFiles += item.count
    })

    if (totalFiles > 0) {
      avgSize = totalWeight / totalFiles
    }
  }

  return Math.round(fileCount * avgSize * 100) / 100 // 保留两位小数
})

const storageTitle = computed(() => {
  return props.stats.totalStorage ? '存储空间（实际）' : '存储空间（估算）'
})

// 方法
const getCategoryName = (category) => {
  const categoryMap = {
    'regular': '常规资料',
    'upgrade': '升学专区'
  }
  return categoryMap[category] || category
}

const getPercentage = (value, total) => {
  if (!total || total === 0) return 0
  return Math.round((value / total) * 100)
}
</script>

<style scoped>
.file-stats-panel {
  padding: 20px;
}

.stats-overview {
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
}

.category-stats {
  margin-bottom: 20px;
}

.ranking-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  min-height: 200px;
}

.stats-list {
  padding: 10px 0;
}

.stats-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.stats-label {
  flex: 0 0 80px;
  font-size: 14px;
  color: #606266;
}

.stats-value {
  flex: 0 0 40px;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  text-align: right;
  margin-right: 12px;
}

.stats-bar {
  flex: 1;
  height: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
}

.stats-bar-fill {
  height: 100%;
  background: #409eff;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.grade-bar {
  background: #67c23a;
}

.subject-bar {
  background: #e6a23c;
}

.ranking-container {
  min-height: 300px;
}

.ranking-index {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.file-title {
  font-weight: 500;
  color: #303133;
}
</style>
