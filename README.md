# K12 Admin Management System

K12教育资源管理后台系统 - 开箱即用版本

## 🚀 快速开始（推荐）

### 方式一：双击运行（Windows）
1. 双击 `run.bat` 文件
2. 等待服务启动完成
3. 浏览器访问：http://localhost:8080

### 方式二：命令行运行
```bash
npm run dev
```

## 📋 系统要求

- **Node.js**: >= 16.0.0 [下载地址](https://nodejs.org/)
- **操作系统**: Windows 10/11, macOS, Linux
- **浏览器**: Chrome, Firefox, Safari, Edge (最新版本)

## 🔧 首次使用配置

### 1. 检查 Node.js 安装
```bash
node --version
npm --version
```

### 2. 环境配置
项目已预配置云开发环境，无需额外配置。如需修改，编辑 `.env` 文件：

```env
# 服务端口（默认无需修改）
PORT=8081
CORS_ORIGIN=http://localhost:8080

# 云开发配置（已预配置）
CLOUDBASE_ENV=cloud1-8gm001v7fd56ff43
CLOUDBASE_SECRET_ID=***
CLOUDBASE_SECRET_KEY=***
```

## 🌟 功能特性

- ✅ **文件管理**: 支持 PDF、DOC、DOCX、PPT、PPTX 格式
- ✅ **智能预览**: 自动生成文档预览图（最多3张）
- ✅ **分类筛选**: 按年级、学科、册别、章节分类
- ✅ **标签系统**: 灵活的标签管理
- ✅ **响应式设计**: 支持桌面和移动端
- ✅ **云存储**: 基于腾讯云开发，稳定可靠

## 🏗️ 技术架构

- **前端**: Vue 3 + Vite + Element Plus + Axios
- **后端**: Node.js + Express + Multer + Sharp
- **云服务**: 腾讯云开发 (CloudBase)
- **数据库**: 云开发数据库 (NoSQL)
- **存储**: 云开发云存储

## 📁 项目结构

```
k12-admin/
├── 📁 frontend/              # 前端项目
│   ├── src/
│   │   ├── components/       # Vue 组件
│   │   ├── views/           # 页面视图
│   │   ├── api/             # API 接口
│   │   └── utils/           # 工具函数
│   ├── package.json
│   └── vite.config.js       # Vite 配置
├── 📁 backend/               # 后端项目
│   ├── src/
│   │   ├── routes/          # 路由
│   │   ├── services/        # 业务逻辑
│   │   ├── middleware/      # 中间件
│   │   └── utils/           # 工具函数
│   └── package.json
├── 📄 .env                   # 环境配置
├── 📄 package.json           # 根项目配置
├── 📄 start.js              # 智能启动脚本
├── 📄 run.bat               # Windows 一键启动
└── 📄 README.md             # 项目文档
```

## 🔧 端口配置

系统使用固定端口，确保团队协作一致性：

- **前端服务**: http://localhost:8080
- **后端API**: http://localhost:8081

如需修改端口，请同时修改以下文件：
1. `.env` 文件中的 `PORT` 和 `CORS_ORIGIN`
2. `frontend/vite.config.js` 中的代理配置
3. `start.js` 中的端口常量

## 📝 使用说明

### 文件上传
1. 点击"上传文件"按钮
2. 选择支持的文件格式（PDF、DOC、DOCX、PPT、PPTX）
3. 填写文件信息（标题、年级、学科等）
4. 系统自动生成预览图并保存

### 文件管理
- **筛选**: 使用左侧筛选面板按条件查找
- **搜索**: 顶部搜索框支持标题搜索
- **预览**: 点击文件卡片查看预览图
- **编辑**: 点击编辑按钮修改文件信息
- **删除**: 点击删除按钮移除文件

## 🚨 常见问题

### Q: 启动时提示端口被占用？
A: 启动脚本会自动检测并清理端口占用，如仍有问题，手动关闭占用进程或重启电脑。

### Q: 文件上传失败？
A: 检查文件格式是否支持，文件大小是否超过50MB限制。

### Q: 预览图不显示？
A: 确保网络连接正常，云存储服务可访问。

### Q: Node.js 版本过低？
A: 请升级到 Node.js 16.0.0 或更高版本。

## 🔄 更新日志

### v1.0.0 (2025-08-16)
- ✅ 完整的文件管理功能
- ✅ 智能预览图生成
- ✅ 响应式界面设计
- ✅ 云存储集成
- ✅ 开箱即用部署方案

## 👥 团队协作

### 开发环境统一
- Node.js 版本: 16.0.0+
- 包管理器: npm
- 代码编辑器: VS Code (推荐)
- 浏览器: Chrome (推荐)

### 部署流程
1. 从代码仓库拉取最新代码
2. 运行 `npm run dev` 或双击 `run.bat`
3. 等待依赖自动安装和服务启动
4. 访问 http://localhost:8080 开始使用

## 📞 技术支持

如遇到问题，请联系开发团队或查看项目文档。

---

**K12 Admin Team** © 2025
