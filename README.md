# K12开发工具&文档

这个目录包含K12项目的开发工具、文档和调试脚本。

## 📁 目录结构

```
k12-dev-tool/
├── docs/                   # 开发文档
│   ├── PRD/               # 产品需求文档
│   ├── API/               # API文档
│   ├── database/          # 数据库设计文档
│   └── deployment/        # 部署文档
├── scripts/               # 调试和工具脚本
│   ├── database/          # 数据库相关脚本
│   ├── test/              # 测试脚本
│   └── deploy/            # 部署脚本
├── tests/                 # 测试用例
│   ├── unit/              # 单元测试
│   ├── integration/       # 集成测试
│   └── e2e/               # 端到端测试
└── tools/                 # 开发辅助工具
    ├── data-generator/    # 测试数据生成器
    ├── config-validator/  # 配置验证工具
    └── performance/       # 性能测试工具
```

## 🔧 主要功能

### 📝 文档管理
- **PRD文档**: 产品需求、功能规格
- **API文档**: 接口设计和使用说明
- **数据库文档**: 表结构、字段说明
- **部署文档**: 环境配置、发布流程

### 🛠️ 调试工具
- **数据库脚本**: 数据初始化、备份恢复
- **测试脚本**: 自动化测试、性能测试
- **部署脚本**: 一键部署、环境切换

### 🧪 测试套件
- **单元测试**: 函数级别测试
- **集成测试**: 模块间交互测试
- **端到端测试**: 完整流程测试

## 🚀 使用方法

### 运行测试脚本
```bash
# 进入开发工具目录
cd k12-dev-tool

# 运行数据库测试
node scripts/test/database-test.js

# 运行API测试
node scripts/test/api-test.js
```

### 生成测试数据
```bash
# 生成测试文件数据
node tools/data-generator/files-generator.js

# 生成用户反馈数据
node tools/data-generator/feedback-generator.js
```

### 验证配置
```bash
# 验证云开发配置
node tools/config-validator/cloudbase-validator.js

# 验证环境变量
node tools/config-validator/env-validator.js
```

## 📋 开发规范

1. **文档更新**: 功能变更时同步更新相关文档
2. **脚本命名**: 使用描述性名称，如 `database-backup.js`
3. **测试覆盖**: 新功能必须包含对应测试用例
4. **工具复用**: 优先使用现有工具，避免重复开发

## 🔗 相关链接

- [K12管理后台](../k12-admin/)
- [K12小程序](../k12-wx/)
- [项目根目录](../)
