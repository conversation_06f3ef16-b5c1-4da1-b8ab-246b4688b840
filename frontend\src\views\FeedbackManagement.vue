<template>
  <div class="feedback-management">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="header-left">
        <h2>💬 用户反馈管理</h2>
        <p class="header-desc">管理用户提交的反馈信息，及时处理用户问题和建议</p>
      </div>
      <div class="header-right">
        <el-button type="success" @click="exportFeedback" :loading="exporting">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
        <el-button @click="loadFeedbackList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-row">
        <div class="filter-item">
          <label>状态：</label>
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="handleFilterChange">
            <el-option label="全部" value="" />
            <el-option label="待处理" value="待处理" />
            <el-option label="处理中" value="处理中" />
            <el-option label="已解决" value="已解决" />
            <el-option label="已关闭" value="已关闭" />
          </el-select>
        </div>

        <div class="filter-item">
          <label>类型：</label>
          <el-select v-model="filters.type" placeholder="选择类型" clearable @change="handleFilterChange">
            <el-option label="全部" value="" />
            <el-option label="功能异常" value="功能异常" />
            <el-option label="功能建议" value="功能建议" />
            <el-option label="内容问题" value="内容问题" />
            <el-option label="其他问题" value="其他问题" />
          </el-select>
        </div>

        <div class="filter-item">
          <label>日期：</label>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleDateChange"
            size="default"
          />
        </div>
      </div>

      <div class="filter-row">
        <div class="filter-item search-item">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索反馈标题或内容..."
            @keyup.enter="handleFilterChange"
            @clear="handleFilterChange"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="filter-actions">
          <el-button @click="handleFilterChange">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>

          <!-- 排序工具 -->
          <div class="sort-tools-inline">
            <span class="sort-label">排序:</span>
            <el-select v-model="sortConfig.field" @change="handleSortChange" style="width: 120px" size="small">
              <el-option label="创建时间" value="created_time" />
              <el-option label="状态" value="feedback_status" />
              <el-option label="类型" value="feedback_type" />
            </el-select>
            <el-button
              @click="toggleSortOrder"
              :type="sortConfig.order === 'desc' ? 'primary' : 'default'"
              size="small"
            >
              <el-icon v-if="sortConfig.order === 'desc'"><ArrowDown /></el-icon>
              <el-icon v-else><ArrowUp /></el-icon>
              {{ sortConfig.order === 'desc' ? '降序' : '升序' }}
            </el-button>
          </div>

          <!-- 每页显示数量 -->
          <div class="page-size-tools">
            <span class="page-size-label">每页:</span>
            <el-select v-model="pagination.pageSize" @change="handlePageSizeChange" style="width: 80px" size="small">
              <el-option label="10" :value="10" />
              <el-option label="20" :value="20" />
              <el-option label="50" :value="50" />
              <el-option label="100" :value="100" />
            </el-select>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 批量操作栏 -->
    <div v-if="selectedFeedbacks.length > 0" class="batch-actions">
      <div class="batch-info">
        已选择 <strong>{{ selectedFeedbacks.length }}</strong> 条反馈
      </div>
      <div class="batch-buttons">
        <el-button type="primary" @click="batchUpdateStatus('处理中')">
          <el-icon><Clock /></el-icon>
          标记为处理中
        </el-button>
        <el-button type="success" @click="batchUpdateStatus('已解决')">
          <el-icon><Check /></el-icon>
          标记为已解决
        </el-button>
        <el-button type="warning" @click="batchUpdateStatus('已关闭')">
          <el-icon><Close /></el-icon>
          标记为已关闭
        </el-button>
        <el-button type="danger" @click="batchDeleteFeedbacks">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 快速统计 -->
    <el-card v-if="stats" class="stats-card" shadow="never">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ stats.total || 0 }}</div>
          <div class="stat-label">总反馈数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value pending">{{ stats.statusStats?.['待处理'] || 0 }}</div>
          <div class="stat-label">待处理</div>
        </div>
        <div class="stat-item">
          <div class="stat-value processing">{{ stats.statusStats?.['处理中'] || 0 }}</div>
          <div class="stat-label">处理中</div>
        </div>
        <div class="stat-item">
          <div class="stat-value resolved">{{ stats.statusStats?.['已解决'] || 0 }}</div>
          <div class="stat-label">已解决</div>
        </div>
        <div class="stat-item">
          <div class="stat-value closed">{{ stats.statusStats?.['已关闭'] || 0 }}</div>
          <div class="stat-label">已关闭</div>
        </div>
        <div class="stat-item">
          <div class="stat-value today">{{ stats.todayCount || 0 }}</div>
          <div class="stat-label">今日新增</div>
        </div>
      </div>
    </el-card>

    <!-- 反馈列表 -->
    <el-card class="table-card">
      <div class="table-header">
        <div class="table-title">
          反馈列表 (共 {{ pagination.total }} 条)
        </div>
      </div>

      <!-- 反馈卡片列表 -->
      <div class="feedback-cards-container" v-loading="loading">
        <div
          v-for="feedback in feedbackList"
          :key="feedback._id"
          class="feedback-card"
          :class="{ 'selected': selectedFeedbacks.includes(feedback._id) }"
        >
          <!-- 卡片头部：选择框、标题、状态 -->
          <div class="card-header">
            <div class="header-left">
              <el-checkbox
                :model-value="selectedFeedbacks.includes(feedback._id)"
                @change="toggleFeedbackSelection(feedback)"
                class="feedback-checkbox"
              />
              <div class="feedback-title-area">
                <div class="feedback-title">{{ feedback.feedback_title }}</div>
                <div class="feedback-meta">
                  <el-tag :type="getTypeTagType(feedback.feedback_type)" size="small">
                    {{ feedback.feedback_type }}
                  </el-tag>
                  <span class="meta-item">{{ formatDate(feedback.created_time) }}</span>
                  <span class="meta-item" v-if="feedback.user_email">{{ feedback.user_email }}</span>
                </div>
              </div>
            </div>
            <div class="header-right">
              <el-select
                v-model="feedback.feedback_status"
                @change="updateFeedbackStatus(feedback._id, feedback.feedback_status)"
                size="small"
                style="width: 100px"
              >
                <el-option label="待处理" value="待处理" />
                <el-option label="处理中" value="处理中" />
                <el-option label="已解决" value="已解决" />
                <el-option label="已关闭" value="已关闭" />
              </el-select>
            </div>
          </div>

          <!-- 卡片主体：反馈内容 -->
          <div class="card-body">
            <div class="feedback-content">
              {{ feedback.feedback_content }}
            </div>
          </div>

          <!-- 卡片底部：操作按钮 -->
          <div class="card-footer">
            <div class="status-badge">
              <el-tag :type="getStatusTagType(feedback.feedback_status)" size="small">
                {{ feedback.feedback_status }}
              </el-tag>
            </div>
            <div class="action-buttons">
              <el-button size="small" type="primary" @click="viewFeedbackDetail(feedback)" plain>
                <el-icon><View /></el-icon>
                查看详情
              </el-button>
              <el-button size="small" type="danger" @click="deleteFeedback(feedback)" plain>
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
          small
        />
      </div>
    </el-card>

    <!-- 反馈详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="反馈详情"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="currentFeedback" class="feedback-detail">
        <div class="detail-row">
          <label>反馈类型：</label>
          <el-tag :type="getTypeTagType(currentFeedback.feedback_type)">
            {{ currentFeedback.feedback_type }}
          </el-tag>
        </div>
        <div class="detail-row">
          <label>反馈标题：</label>
          <span>{{ currentFeedback.feedback_title }}</span>
        </div>
        <div class="detail-row">
          <label>反馈内容：</label>
          <div class="content-text">{{ currentFeedback.feedback_content }}</div>
        </div>
        <div class="detail-row">
          <label>用户邮箱：</label>
          <span>{{ currentFeedback.user_email || '未提供' }}</span>
        </div>
        <div class="detail-row">
          <label>当前状态：</label>
          <el-tag :type="getStatusTagType(currentFeedback.feedback_status)">
            {{ currentFeedback.feedback_status }}
          </el-tag>
        </div>
        <div class="detail-row">
          <label>创建时间：</label>
          <span>{{ formatDate(currentFeedback.created_time) }}</span>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button type="primary" @click="showDetailDialog = false">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Download, Refresh, Search, ArrowDown, ArrowUp, Clock, Check, Close, Delete, View
} from '@element-plus/icons-vue'
import { feedbackApi } from '@/api/feedback'

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const feedbackList = ref([])
const selectedFeedbacks = ref([])
const stats = ref(null)
const showDetailDialog = ref(false)
const currentFeedback = ref(null)
const dateRange = ref([])

// 筛选器
const filters = reactive({
  status: '',
  type: '',
  keyword: '',
  startDate: '',
  endDate: ''
})

// 排序配置
const sortConfig = reactive({
  field: 'created_time',
  order: 'desc'
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 生命周期
onMounted(() => {
  loadFeedbackList()
  loadFeedbackStats()
})

// 方法
const loadFeedbackList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...filters,
      sortBy: sortConfig.field,
      sortOrder: sortConfig.order
    }

    const result = await feedbackApi.getFeedbackList(params)
    if (result.success) {
      feedbackList.value = result.data.feedbacks || []
      pagination.total = result.data.total
    }
  } catch (error) {
    ElMessage.error('加载反馈列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const loadFeedbackStats = async () => {
  try {
    const result = await feedbackApi.getFeedbackStats()
    if (result.success) {
      stats.value = result.data
    }
  } catch (error) {
    console.error('加载反馈统计失败:', error)
  }
}

const handleFilterChange = () => {
  pagination.page = 1
  loadFeedbackList()
}

const resetFilters = () => {
  Object.assign(filters, {
    status: '',
    type: '',
    keyword: '',
    startDate: '',
    endDate: ''
  })
  dateRange.value = []
  Object.assign(sortConfig, {
    field: 'created_time',
    order: 'desc'
  })
  handleFilterChange()
}

const handleDateChange = (dates) => {
  if (dates && dates.length === 2) {
    filters.startDate = dates[0].toISOString().split('T')[0]
    filters.endDate = dates[1].toISOString().split('T')[0]
  } else {
    filters.startDate = ''
    filters.endDate = ''
  }
  handleFilterChange()
}

// 排序处理
const handleSortChange = () => {
  pagination.page = 1
  loadFeedbackList()
}

const toggleSortOrder = () => {
  sortConfig.order = sortConfig.order === 'desc' ? 'asc' : 'desc'
  handleSortChange()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadFeedbackList()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadFeedbackList()
}

// 选择处理
const toggleFeedbackSelection = (feedback) => {
  const index = selectedFeedbacks.value.indexOf(feedback._id)
  if (index > -1) {
    selectedFeedbacks.value.splice(index, 1)
  } else {
    selectedFeedbacks.value.push(feedback._id)
  }
}

// 更新反馈状态
const updateFeedbackStatus = async (id, status) => {
  try {
    const result = await feedbackApi.updateFeedbackStatus(id, status)
    if (result.success) {
      ElMessage.success('状态更新成功')
      loadFeedbackStats() // 更新统计数据
    }
  } catch (error) {
    ElMessage.error('状态更新失败: ' + error.message)
    loadFeedbackList() // 恢复原状态
  }
}

// 批量更新状态
const batchUpdateStatus = async (status) => {
  try {
    const result = await feedbackApi.batchUpdateStatus(selectedFeedbacks.value, status)
    if (result.success) {
      ElMessage.success(`批量更新状态成功`)
      selectedFeedbacks.value = []
      loadFeedbackList()
      loadFeedbackStats()
    } else {
      ElMessage.error('批量更新失败: ' + result.error)
    }
  } catch (error) {
    ElMessage.error('批量更新失败: ' + error.message)
  }
}

// 查看反馈详情
const viewFeedbackDetail = async (feedback) => {
  try {
    const result = await feedbackApi.getFeedbackDetail(feedback._id)
    if (result.success) {
      currentFeedback.value = result.data
      showDetailDialog.value = true
    }
  } catch (error) {
    ElMessage.error('获取反馈详情失败: ' + error.message)
  }
}

// 删除反馈
const deleteFeedback = async (feedback) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除反馈 "${feedback.feedback_title}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await feedbackApi.deleteFeedback(feedback._id)
    if (result.success) {
      ElMessage.success('删除成功')
      loadFeedbackList()
      loadFeedbackStats()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 批量删除反馈
const batchDeleteFeedbacks = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedFeedbacks.value.length} 条反馈吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await feedbackApi.batchDeleteFeedback(selectedFeedbacks.value)
    if (result.success) {
      ElMessage.success('批量删除成功')
      selectedFeedbacks.value = []
      loadFeedbackList()
      loadFeedbackStats()
    } else {
      ElMessage.error('批量删除失败: ' + result.error)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败: ' + error.message)
    }
  }
}

// 导出反馈数据
const exportFeedback = async () => {
  try {
    exporting.value = true
    const result = await feedbackApi.exportFeedback(filters)

    // 创建下载链接
    const blob = new Blob([result], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `feedback_export_${new Date().toISOString().split('T')[0]}.csv`
    link.click()
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败: ' + error.message)
  } finally {
    exporting.value = false
  }
}

// 工具函数
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

const getStatusTagType = (status) => {
  const statusMap = {
    '待处理': 'warning',
    '处理中': 'primary',
    '已解决': 'success',
    '已关闭': 'info'
  }
  return statusMap[status] || ''
}

const getTypeTagType = (type) => {
  const typeMap = {
    '功能异常': 'danger',
    '功能建议': 'success',
    '内容问题': 'warning',
    '其他问题': 'info'
  }
  return typeMap[type] || ''
}
</script>

<style scoped>
.feedback-management {
  padding: 24px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-card :deep(.el-card__body) {
  padding: 24px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 160px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  min-width: 40px;
  font-weight: 500;
}

.search-item {
  flex: 1;
}

.filter-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.sort-tools-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
  padding-left: 16px;
  border-left: 1px solid #e4e7ed;
}

.sort-label {
  font-size: 12px;
  color: #606266;
  font-weight: 600;
  white-space: nowrap;
}

.page-size-tools {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
  padding-left: 16px;
  border-left: 1px solid #e4e7ed;
}

.page-size-label {
  font-size: 12px;
  color: #606266;
  font-weight: 600;
  white-space: nowrap;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #409eff;
}

.batch-info {
  color: #606266;
}

.batch-buttons {
  display: flex;
  gap: 12px;
}

.stats-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-value.pending {
  color: #e6a23c;
}

.stat-value.processing {
  color: #409eff;
}

.stat-value.resolved {
  color: #67c23a;
}

.stat-value.closed {
  color: #909399;
}

.stat-value.today {
  color: #f56c6c;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-card :deep(.el-card__body) {
  padding: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.table-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.feedback-cards-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feedback-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  background: white;
  transition: all 0.3s ease;
}

.feedback-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}

.feedback-card.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.feedback-checkbox {
  margin-top: 4px;
}

.feedback-title-area {
  flex: 1;
}

.feedback-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1.4;
}

.feedback-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.meta-item {
  font-size: 12px;
  color: #909399;
}

.header-right {
  margin-left: 16px;
}

.card-body {
  margin-bottom: 16px;
}

.feedback-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-badge {
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.feedback-detail {
  padding: 20px 0;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 12px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  font-size: 14px;
}

.content-text {
  flex: 1;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #409eff;
  line-height: 1.6;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .feedback-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .filter-item {
    min-width: auto;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
  }

  .header-right {
    margin-left: 0;
    align-self: flex-start;
  }

  .card-footer {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .detail-row {
    flex-direction: column;
    gap: 8px;
  }

  .detail-row label {
    min-width: auto;
  }
}
</style>
