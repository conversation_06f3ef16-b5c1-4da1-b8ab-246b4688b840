# K12教育资源管理后台 - 开发需求文档

## 1. 项目概述

### 1.1 项目背景
基于现有的K12教育资源小程序（使用微信云开发），开发一个本地运行的后台管理系统，主要解决files集合的批量管理和系统配置功能。

### 1.2 技术架构
- **前端**: Vue 3 + Element Plus + Vite
- **后端**: Node.js + Express + 云开发服务端SDK
- **数据库**: 腾讯云开发数据库（直接操作）
- **文件存储**: 腾讯云开发云存储
- **部署方式**: 本地开发运行

### 1.3 核心价值
- 提升文件管理效率，支持批量操作
- 自动化预览图生成和图片压缩
- 统一的系统配置管理界面
- 数据统计和分析功能

## 2. 功能需求

### 2.1 文件管理模块（核心功能）

#### 2.1.1 批量文件上传
**功能描述**: 支持多种方式批量上传PDF、Word等教育资源文件

**基础上传功能**:
- 支持拖拽上传，多文件选择
- 文件类型限制：PDF、DOC、DOCX、PPT、PPTX
- 单文件大小限制：50MB
- 批量设置文件属性（年级、科目、册别、板块等）
- 上传进度显示和错误处理

**CSV批量上传功能**:
- 支持CSV文件配置批量上传任务
- 文件路径自动识别和验证
- 元数据预填充和批量编辑
- 上传前预览和校准功能
- 智能错误检测和修复建议
- CSV模板下载功能

**CSV文件格式**:
```csv
文件路径,标题,描述,年级,科目,册别,板块,分类,标签,排序权重,广告次数
D:\files\一年级数学.pdf,一年级数学练习,数学基础练习题,一年级,数学,上册,单元同步,regular,"基础练习",100,1
```

**技术要求**:
- 自动提取文档前3页作为预览图
- 预览图压缩优化（400x600px，质量80%）
- 文件元数据自动提取（页数、大小、类型）
- CSV解析和验证服务
- 批量上传进度跟踪

#### 2.1.2 文件列表管理
**功能描述**: 文件的查看、编辑、删除等管理功能

**详细需求**:
- 分页列表展示，支持搜索和筛选
- 按年级、科目、册别、板块筛选
- 批量操作：启用/禁用、删除、修改分类
- 文件详情编辑：标题、描述、分类、标签等
- 预览图管理：查看、替换、删除

#### 2.1.3 文件统计分析
**功能描述**: 文件数据统计和分析

**详细需求**:
- 文件总数统计（按分类）
- 下载量排行榜
- 上传趋势分析
- 存储空间使用情况

### 2.2 系统配置模块

#### 2.2.1 广告配置管理
**功能描述**: 管理小程序的广告位配置

**详细需求**:
- 广告位开关控制
- 广告ID配置
- 广告类型设置（横幅、激励视频等）
- 配置实时生效

#### 2.2.2 系统参数设置
**功能描述**: 系统运行参数配置

**详细需求**:
- 热门搜索关键词管理
- 下载限制参数设置
- 文件分类标准值管理
- 系统公告设置

### 2.3 用户反馈管理

#### 2.3.1 反馈查看处理
**功能描述**: 查看和处理用户反馈

**详细需求**:
- 反馈列表查看
- 反馈状态管理（待处理、处理中、已解决）
- 反馈分类统计
- 导出反馈数据

### 2.4 数据备份与恢复

#### 2.4.1 数据导出
**功能描述**: 数据备份和导出功能

**详细需求**:
- files集合数据导出（Excel/JSON）
- 系统配置导出
- 定期自动备份（可选）

## 3. 技术实现方案

### 3.1 系统架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API       │    │   云开发数据库   │
│  Vue 3 + EP     │◄──►│ Express + SDK   │◄──►│   MongoDB       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   云开发存储     │
                       │   文件上传       │
                       └─────────────────┘
```

### 3.2 核心技术栈

#### 3.2.1 后端技术
- **框架**: Express.js + 云开发SDK
- **文件处理**: sharp, pdf-poppler, mammoth
- **CSV处理**: csv-parser, csv-writer
- **工具库**: multer, joi, winston

#### 3.2.2 前端技术
- **框架**: Vue 3 + Element Plus + Vite
- **状态管理**: Pinia
- **HTTP客户端**: Axios

## 4. 核心功能验收标准

### 4.1 文件管理功能
- ✅ 支持拖拽和CSV批量上传
- ✅ 自动生成预览图并压缩
- ✅ 文件分类管理和批量操作
- ✅ CSV模板下载和智能验证

### 4.2 系统配置功能
- ✅ 广告配置实时生效
- ✅ 系统参数动态管理
- ✅ 用户反馈处理

### 4.3 性能要求
- 单次批量上传支持50个文件
- 预览图生成时间<30秒/文件
- CSV解析和验证<5秒
- 界面响应时间<2秒

### 4.4 安全要求
- 云开发密钥安全存储
- 文件类型和大小验证
- 本地访问控制
- 操作日志记录
