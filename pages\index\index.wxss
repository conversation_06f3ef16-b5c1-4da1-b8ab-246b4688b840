      /* 首页样式 - 现代教育应用设计 + 微交互动画增强 */

.container {
  padding-bottom: 200rpx;
  background: linear-gradient(180deg, #1677FF 0%, #69B1FF 100%);
  min-height: 100vh;
  animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 顶部区域 */
.header-section {
  padding: 60rpx 40rpx 70rpx;
  color: white;
  animation: slideInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.app-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 40rpx;
  color: #FFFFFF;
  text-align: center;
  letter-spacing: 2rpx;
  line-height: 1.2;
  animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

.app-subtitle {
  font-size: 28rpx;
  font-weight: 400;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  margin-bottom: 40rpx;
  padding: 0 40rpx;
  letter-spacing: 4rpx;
  word-spacing: 16rpx;
  animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
}

.app-notice {
  font-size: 24rpx;
  font-weight: 500;
  color: #FFFFFF;
  text-align: center;
  margin-bottom: 48rpx;
  padding: 20rpx 40rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-radius: 40rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  line-height: 1.4;
  letter-spacing: 2rpx;
  animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-40rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 搜索框样式 - 现代化设计 + 微交互动画 */
.search-box {
  display: flex;
  align-items: center;
  height: 96rpx;
  padding: 0 40rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 48rpx;
  margin: 0 32rpx 32rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
  z-index: 999;
  cursor: pointer;
  pointer-events: auto;
  touch-action: manipulation;
  min-height: 96rpx;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-box::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.15) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
  z-index: 0;
}

.search-box:active::after {
  width: 400rpx;
  height: 400rpx;
}

.search-box:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.15);
}

.search-icon {
  font-size: 36rpx;
  margin-right: 24rpx;
  opacity: 0.6;
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-placeholder {
  flex: 1;
  font-size: 32rpx;
  color: #666;
  font-weight: 400;
  position: relative;
  z-index: 1;
}

/* 主要内容区域 */
.main-content {
  background-color: #F8F9FA;
  border-radius: 48rpx 48rpx 0 0;
  margin-top: 16rpx;
  padding-top: 32rpx;
  min-height: calc(100vh - 400rpx);
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.5s both;
}

/* 年级导航样式 - 卡片式设计 + 微交互动画 */
.grade-section {
  padding: 0 40rpx 48rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.6s both;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.section-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 0;
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #1677FF, #69B1FF);
  border-radius: 4rpx;
  animation: expandWidth 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.8s both;
  transform: scaleX(0);
  transform-origin: left;
}

@keyframes expandWidth {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

.more-btn {
  font-size: 28rpx;
  color: #1677FF;
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.more-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
  pointer-events: none;
  z-index: 0;
}

.more-btn:active::after {
  width: 120rpx;
  height: 120rpx;
}

.more-btn:active {
  transform: scale(0.95);
}

.grade-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.grade-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background: linear-gradient(135deg, #FFFFFF 0%, #F8FAFF 100%);
  border-radius: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(22, 119, 255, 0.08);
  border: 2rpx solid rgba(22, 119, 255, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateY(60rpx);
}

/* 错位出现动画 */
.grade-item:nth-child(1) { animation-delay: 0.7s; }
.grade-item:nth-child(2) { animation-delay: 0.8s; }
.grade-item:nth-child(3) { animation-delay: 0.9s; }
.grade-item:nth-child(4) { animation-delay: 1.0s; }
.grade-item:nth-child(5) { animation-delay: 1.1s; }
.grade-item:nth-child(6) { animation-delay: 1.2s; }

.grade-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #1677FF, #69B1FF);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

/* 涟漪效果 */
.grade-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
  z-index: 0;
}

.grade-item:active {
  transform: translateY(-4rpx) scale(0.98);
  box-shadow: 0 16rpx 50rpx rgba(22, 119, 255, 0.15);
}

.grade-item:active::before {
  transform: scaleX(1);
}

.grade-item:active::after {
  width: 240rpx;
  height: 240rpx;
}

.grade-icon {
  width: 112rpx;
  height: 112rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1677FF 0%, #69B1FF 100%);
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: 600;
  border-radius: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(22, 119, 255, 0.3);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-text {
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
}

.grade-item:active .grade-icon {
  transform: scale(0.9);
}

.grade-icon::after {
  content: '';
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  width: 16rpx;
  height: 16rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}

/* 不同年级图标颜色区分 */
.grade-icon-1 {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
}

.grade-icon-2 {
  background: linear-gradient(135deg, #4ECDC4 0%, #6ED5CE 100%);
}

.grade-icon-3 {
  background: linear-gradient(135deg, #45B7D1 0%, #67C4DD 100%);
}

.grade-icon-4 {
  background: linear-gradient(135deg, #96CEB4 0%, #A8D5C1 100%);
}

.grade-icon-5 {
  background: linear-gradient(135deg, #FFEAA7 0%, #FDCB6E 100%);
}

.grade-icon-6 {
  background: linear-gradient(135deg, #DDA0DD 0%, #E6B3E6 100%);
}

.grade-text {
  font-size: 30rpx;
  color: #333333;
  font-weight: 600;
  text-align: center;
  position: relative;
  z-index: 1;
  transition: color 0.3s ease;
}

.grade-item:active .grade-text {
  color: #1677FF;
}

/* 升学专区样式 - 特殊板块设计 */
.upgrade-section {
  padding: 0 40rpx 48rpx;
  margin-top: 16rpx;
}

.upgrade-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 32rpx;
}

.upgrade-item {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg, #FFF8E1 0%, #FFFBF0 100%);
  border-radius: 32rpx;
  box-shadow: 0 8rpx 40rpx rgba(255, 193, 7, 0.15);
  border: 4rpx solid rgba(255, 193, 7, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateY(60rpx);
  flex: 1;
  min-width: 0;
  width: calc(50% - 16rpx);
  box-sizing: border-box;
}

.upgrade-item:nth-child(1) { 
  animation-delay: 1.3s; 
  background: linear-gradient(135deg, #FFF3E0 0%, #FFF8F0 100%);
  border-color: rgba(255, 152, 0, 0.3);
  box-shadow: 0 8rpx 40rpx rgba(255, 152, 0, 0.15);
}

.upgrade-item:nth-child(2) { 
  animation-delay: 1.4s; 
  background: linear-gradient(135deg, #E8F5E8 0%, #F0FBF0 100%);
  border-color: rgba(76, 175, 80, 0.3);
  box-shadow: 0 8rpx 40rpx rgba(76, 175, 80, 0.15);
}

.upgrade-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 8rpx;
  background: linear-gradient(90deg, #FF9800, #FFC107);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.upgrade-item:nth-child(2)::before {
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
}

.upgrade-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 152, 0, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
  z-index: 0;
}

.upgrade-item:nth-child(2)::after {
  background: radial-gradient(circle, rgba(76, 175, 80, 0.2) 0%, transparent 70%);
}

.upgrade-item:active {
  transform: translateY(-6rpx) scale(0.98);
  box-shadow: 0 16rpx 60rpx rgba(255, 152, 0, 0.25);
}

.upgrade-item:nth-child(2):active {
  box-shadow: 0 16rpx 60rpx rgba(76, 175, 80, 0.25);
}

.upgrade-item:active::before {
  transform: scaleX(1);
}

.upgrade-item:active::after {
  width: 300rpx;
  height: 300rpx;
}

.upgrade-icon {
  width: 96rpx;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #FF9800 0%, #FFC107 100%);
  border-radius: 28rpx;
  margin-right: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 152, 0, 0.3);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
}

.upgrade-icon-2 {
  background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
  box-shadow: 0 12rpx 32rpx rgba(76, 175, 80, 0.3);
}

.upgrade-item:active .upgrade-icon {
  transform: scale(0.9) rotate(5deg);
}

.upgrade-emoji {
  font-size: 40rpx;
  color: #FFFFFF;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.upgrade-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.upgrade-title {
  font-size: 30rpx;
  font-weight: 700;
  color: #E65100;
  margin-bottom: 6rpx;
  transition: color 0.3s ease;
  line-height: 1.2;
}

.upgrade-item:nth-child(2) .upgrade-title {
  color: #2E7D32;
}

.upgrade-item:active .upgrade-title {
  color: #FF6F00;
}

.upgrade-item:nth-child(2):active .upgrade-title {
  color: #1B5E20;
}

.upgrade-subtitle {
  font-size: 24rpx;
  font-weight: 500;
  color: #BF360C;
  opacity: 0.8;
  transition: all 0.3s ease;
  line-height: 1.2;
}

.upgrade-item:nth-child(2) .upgrade-subtitle {
  color: #1B5E20;
}

.upgrade-item:active .upgrade-subtitle {
  opacity: 1;
  transform: translateY(-2rpx);
}

/* 推荐资料样式 - 现代卡片设计 + 微交互动画 */
.recommend-section {
  padding: 0 32rpx 48rpx;
}

.material-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.material-item {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  background: linear-gradient(135deg, #FFFFFF 0%, #FAFBFF 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid rgba(22, 119, 255, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: slideInLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateX(-60rpx);
  width: 100%;
  box-sizing: border-box;
  gap: 12rpx;
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 错位出现动画 */
.material-item:nth-child(1) { animation-delay: 1.3s; }
.material-item:nth-child(2) { animation-delay: 1.4s; }
.material-item:nth-child(3) { animation-delay: 1.5s; }
.material-item:nth-child(4) { animation-delay: 1.6s; }

.material-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 8rpx;
  height: 100%;
  background: linear-gradient(180deg, #1677FF, #69B1FF);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

/* 涟漪效果 */
.material-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.8s ease, height 0.8s ease;
  pointer-events: none;
  z-index: 0;
}

.material-item:active {
  transform: translateY(-4rpx) scale(0.995);
  box-shadow: 0 16rpx 60rpx rgba(22, 119, 255, 0.12);
}

.material-item:active::before {
  transform: scaleY(1);
}

.material-item:active::after {
  width: 400rpx;
  height: 400rpx;
}

/* 新布局样式 */

/* 标题行 */
.title-row {
  margin-bottom: 8rpx;
}

.material-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
}

.material-item:active .material-title {
  color: #1677FF;
}

/* 文件信息行：图标 + 页数 + 大小 + 特征 */
.file-info-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
}

.file-icon {
  width: 24rpx;
  height: 24rpx;
  flex-shrink: 0;
  margin-right: 6rpx;
}

.file-pages {
  font-size: 22rpx;
  color: #666;
  font-weight: 600;
  flex-shrink: 0;
}

.file-size {
  font-size: 20rpx;
  color: #999;
  font-weight: 500;
  flex-shrink: 0;
}

.feature-tags-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 6rpx;
  flex: 1;
}

.feature-tag-small {
  background: linear-gradient(135deg, #FFF7E6 0%, #FFFBF0 100%);
  color: #FF8C00;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  font-weight: 600;
  white-space: nowrap;
  text-align: center;
  line-height: 1.2;
  border: 1rpx solid rgba(255, 140, 0, 0.2);
  transition: all 0.2s ease;
}

.feature-tag-small:active {
  transform: scale(0.95);
}

/* 标签行 */
.tags-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 8rpx;
  align-items: center;
}

.category-tag {
  padding: 4rpx 12rpx;
  background: linear-gradient(135deg, #E6F4FF 0%, #F0F7FF 100%);
  color: #1677FF;
  font-size: 22rpx;
  font-weight: 500;
  border-radius: 16rpx;
  border: 1rpx solid rgba(22, 119, 255, 0.15);
  transition: all 0.3s ease;
}

.feature-tag {
  background: linear-gradient(135deg, #FFF7E6 0%, #FFFBF0 100%);
  color: #FF8C00;
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  white-space: nowrap;
  text-align: center;
  line-height: 1.2;
  border: 1rpx solid rgba(255, 140, 0, 0.2);
  transition: all 0.2s ease;
}

.feature-tag:active {
  transform: scale(0.95);
}

/* 数据积分一行 */
.stats-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.material-stats {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
  gap: 4rpx;
  transition: color 0.3s ease;
}

.material-item:active .stat-item {
  color: #1677FF;
}


/* 加载和空状态样式 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  color: #999999;
  font-size: 28rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  color: #999999;
  animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

.empty-text {
  font-size: 32rpx;
  color: #999999;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .grade-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
  }
  
  .upgrade-grid {
    flex-direction: column;
  }
  
  .upgrade-item {
    width: 100%;
    margin-bottom: 24rpx;
  }
  
  .material-item {
    flex-direction: column;
    text-align: center;
    padding: 24rpx;
  }
  
  .material-cover {
    width: 120rpx;
    height: 120rpx;
    margin: 0 auto 16rpx;
  }
  
  .material-info {
    margin-left: 0;
    margin-top: 0;
  }
}

/* 小屏幕适配 */
@media (max-width: 600rpx) {
  .grade-grid {
    grid-template-columns: 1fr;
  }
  
  .upgrade-item {
    padding: 24rpx 20rpx;
  }
  
  .upgrade-icon {
    width: 80rpx;
    height: 80rpx;
    margin-right: 16rpx;
  }
  
  .upgrade-emoji {
    font-size: 32rpx;
  }
  
  .upgrade-title {
    font-size: 26rpx;
  }
  
  .upgrade-subtitle {
    font-size: 22rpx;
  }
}

/* 性能优化 */
.container,
.header-section,
.search-box,
.main-content,
.grade-section,
.recommend-section {
  will-change: transform, opacity;
}

.grade-item,
.material-item {
  will-change: transform, box-shadow;
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .container,
  .header-section,
  .search-box,
  .main-content,
  .grade-section,
  .recommend-section,
  .grade-item,
  .material-item {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

