// 用户标识管理器
const { authManager } = require('./authManager')

class UserManager {
  constructor() {
    this.userId = null
    this.userType = null // 'openid' | 'device'
    this.initialized = false
  }

  // 初始化用户标识
  async initUser() {
    if (this.initialized) {
      return {
        userId: this.userId,
        userType: this.userType
      }
    }

    try {
      // 1. 检查缓存的真实openid
      const cachedOpenId = wx.getStorageSync('user_openid')
      if (cachedOpenId && !cachedOpenId.startsWith('device_')) {
        this.userId = cachedOpenId
        this.userType = 'openid'
        this.initialized = true
        console.log('使用缓存的openid:', cachedOpenId)
        return { userId: this.userId, userType: this.userType }
      }

      // 2. 尝试通过授权管理器获取真实openid
      try {
        const authResult = await authManager.getOpenId()

        if (authResult.success) {
          this.userId = authResult.openid
          this.userType = 'openid'
          this.initialized = true
          console.log('✅ 获取真实openid成功:', this.userId, '方法:', authResult.method)
          return { userId: this.userId, userType: this.userType }
        }

        console.log('⚠️ 无法获取真实openid:', authResult.error)
      } catch (cloudError) {
        console.log('❌ 授权管理器获取openid失败:', cloudError.message)
      }

      // 3. 使用设备ID
      this.userId = this.getDeviceId()
      this.userType = 'device'
      this.initialized = true
      console.log('使用设备ID:', this.userId)
      
      return { userId: this.userId, userType: this.userType }

    } catch (error) {
      console.error('初始化用户标识失败:', error)
      // 最终降级
      this.userId = this.getDeviceId()
      this.userType = 'device'
      this.initialized = true
      return { userId: this.userId, userType: this.userType }
    }
  }

  // 获取设备ID
  getDeviceId() {
    let deviceId = wx.getStorageSync('device_user_id')
    if (!deviceId) {
      try {
        // 使用新的API替代已弃用的wx.getSystemInfoSync
        const deviceInfo = wx.getDeviceInfo()
        const appInfo = wx.getAppBaseInfo()
        const windowInfo = wx.getWindowInfo()

        const deviceString = `${deviceInfo.platform}_${deviceInfo.model}_${appInfo.version}`.replace(/\s+/g, '_')
        const timestamp = Date.now()
        const random = Math.random().toString(36).substring(2, 8)
        deviceId = `device_${deviceString}_${timestamp}_${random}`
      } catch (error) {
        // 降级方案
        deviceId = `device_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      }
      wx.setStorageSync('device_user_id', deviceId)
    }
    return deviceId
  }

  // 获取当前用户ID
  async getUserId() {
    if (!this.initialized) {
      await this.initUser()
    }
    return this.userId
  }

  // 获取用户类型
  getUserType() {
    return this.userType
  }

  // 检查是否为真实用户
  isRealUser() {
    return this.userType === 'openid'
  }

  // 重置用户状态（用于重新登录）
  reset() {
    this.userId = null
    this.userType = null
    this.initialized = false
    wx.removeStorageSync('user_openid')
  }

  // 强制刷新用户标识
  async refresh() {
    this.reset()
    return await this.initUser()
  }

  // 获取用户显示信息
  getUserDisplayInfo() {
    if (!this.initialized) {
      return { display: '未知用户', type: 'unknown' }
    }

    if (this.userType === 'openid') {
      return {
        display: `微信用户 ${this.userId.substring(0, 8)}...`,
        type: 'wechat'
      }
    } else {
      return {
        display: `设备用户 ${this.userId.substring(7, 15)}...`,
        type: 'device'
      }
    }
  }

  // 检查用户权限等级
  getUserPermissionLevel() {
    if (this.userType === 'openid') {
      return 'full' // 完整权限
    } else {
      return 'limited' // 受限权限
    }
  }

  // 获取用户统计标识（用于数据分析）
  getAnalyticsId() {
    if (this.userType === 'openid') {
      // 对真实openid进行哈希处理，保护隐私
      return `hash_${this.simpleHash(this.userId)}`
    } else {
      return this.userId
    }
  }

  // 简单哈希函数
  simpleHash(str) {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString(36)
  }

  // 检查是否需要升级到真实用户
  shouldUpgradeToRealUser() {
    return this.userType === 'device'
  }

  // 尝试升级到真实用户
  async tryUpgradeToRealUser() {
    if (this.userType === 'openid') {
      return { success: true, message: '已经是真实用户' }
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'getOpenId',
        data: {}
      })

      if (result.result && result.result.success) {
        const oldUserId = this.userId
        this.userId = result.result.openid
        this.userType = 'openid'

        wx.setStorageSync('user_openid', this.userId)
        console.log('用户升级成功:', oldUserId, '->', this.userId)

        return {
          success: true,
          message: '升级为真实用户成功',
          oldUserId: oldUserId,
          newUserId: this.userId
        }
      } else {
        return {
          success: false,
          message: '升级失败，继续使用设备ID'
        }
      }
    } catch (error) {
      console.error('用户升级失败:', error)
      return {
        success: false,
        message: '升级失败，网络错误'
      }
    }
  }

  // 测试云函数调用（调试用）
  async testCloudFunction() {
    try {
      console.log('🧪 测试云函数调用...')

      // 检查云开发初始化状态
      if (!wx.cloud) {
        return {
          success: false,
          error: 'cloud_not_supported',
          message: '云开发不支持'
        }
      }

      // 调用云函数
      const result = await wx.cloud.callFunction({
        name: 'getOpenId',
        data: {
          test: true,
          timestamp: Date.now()
        }
      })

      console.log('🧪 云函数调用结果:', result)

      return {
        success: true,
        cloudFunctionResult: result.result,
        rawResult: result
      }

    } catch (error) {
      console.error('🧪 云函数调用失败:', error)
      return {
        success: false,
        error: error.errMsg || error.message,
        details: error
      }
    }
  }

  // 强制重新获取openid（调试用）
  async forceGetOpenId() {
    try {
      console.log('🔄 强制获取openid...')

      // 清除缓存
      wx.removeStorageSync('user_openid')

      // 重新调用云函数
      const result = await wx.cloud.callFunction({
        name: 'getOpenId',
        data: {
          force: true,
          timestamp: Date.now()
        }
      })

      console.log('🔄 强制获取结果:', result)

      if (result.result && result.result.success) {
        this.userId = result.result.openid
        this.userType = 'openid'
        this.initialized = true

        wx.setStorageSync('user_openid', this.userId)

        return {
          success: true,
          openid: this.userId,
          message: '强制获取openid成功'
        }
      } else {
        return {
          success: false,
          error: result.result?.error || 'unknown',
          message: result.result?.message || '获取失败',
          debug: result.result?.debug
        }
      }

    } catch (error) {
      console.error('🔄 强制获取openid失败:', error)
      return {
        success: false,
        error: error.errMsg || error.message,
        details: error
      }
    }
  }
}

// 创建全局实例
const userManager = new UserManager()

module.exports = {
  userManager,
  UserManager
}
