// 更新文件浏览量云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { file_id } = event

  try {
    // 验证参数
    if (!file_id) {
      return {
        success: false,
        error: 'invalid_params',
        message: '缺少文件ID参数'
      }
    }

    // 检查文件是否存在
    const fileExists = await db.collection('files').doc(file_id).get()
    if (!fileExists.data) {
      return {
        success: false,
        error: 'file_not_found',
        message: '文件不存在'
      }
    }

    // 更新浏览次数
    const result = await db.collection('files')
      .doc(file_id)
      .update({
        data: {
          view_count: db.command.inc(1),
          updated_time: new Date()
        }
      })

    return {
      success: true,
      message: '浏览量更新成功',
      stats: result.stats
    }

  } catch (error) {
    console.error('更新浏览量失败:', error)
    return {
      success: false,
      error: 'update_failed',
      message: '更新浏览量失败'
    }
  }
}
