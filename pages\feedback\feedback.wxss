/* pages/feedback/feedback.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 页面标题 */
.page-header {
  padding: 60rpx 32rpx 40rpx;
  background-color: #4285f4;
  color: white;
  text-align: center;
}

.page-title {
  display: block;
  font-size: 44rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.page-subtitle {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
}

/* 反馈表单 */
.feedback-form {
  padding: 32rpx;
}

.form-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.required {
  color: #ff4d4f;
  font-size: 26rpx;
}

.optional {
  color: #999;
  font-size: 24rpx;
}

/* 选择器 */
.picker-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e8e8e8;
  min-height: 80rpx;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  color: #999;
  font-size: 20rpx;
}

/* 输入框 */
.input-field {
  width: 100%;
  padding: 24rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e8e8e8;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  min-height: 80rpx;
}

.input-field:focus {
  border-color: #4285f4;
  background-color: white;
}

.textarea-field {
  width: 100%;
  min-height: 240rpx;
  padding: 24rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e8e8e8;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 1.6;
}

.textarea-field:focus {
  border-color: #4285f4;
  background-color: white;
}

.input-counter {
  text-align: right;
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

.input-hint {
  font-size: 22rpx;
  color: #666;
  margin-top: 8rpx;
}

/* 操作按钮 */
.action-buttons {
  padding: 0 32rpx;
  margin-top: 32rpx;
  display: flex;
  gap: 16rpx;
}

.secondary-button {
  flex: 1;
  padding: 20rpx;
  background-color: white;
  color: #4285f4;
  border: 1rpx solid #4285f4;
  border-radius: 8rpx;
  text-align: center;
  font-size: 28rpx;
}

.secondary-button:active {
  background-color: #f0f0f0;
}

.primary-button {
  flex: 2;
  padding: 20rpx;
  background-color: #4285f4;
  color: white;
  border-radius: 8rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
}

.primary-button:active {
  background-color: #3367d6;
}

.primary-button.disabled {
  background-color: #ccc;
  color: white;
}

/* 提示信息 */
.tips-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 32rpx 32rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.tips-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.tips-list {
  padding-left: 0;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}