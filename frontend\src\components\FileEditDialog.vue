<template>
  <el-dialog
    v-model="visible"
    title="编辑文件信息"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="edit-content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="left"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="文件标题" prop="title">
              <el-input
                v-model="form.title"
                placeholder="请输入文件标题"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="文件描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                :rows="3"
                placeholder="请输入文件描述"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="年级" prop="grade">
              <el-select v-model="form.grade" placeholder="选择年级" clearable>
                <el-option
                  v-for="grade in gradeOptions"
                  :key="grade"
                  :label="grade"
                  :value="grade"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="科目" prop="subject">
              <el-select v-model="form.subject" placeholder="选择科目">
                <el-option
                  v-for="subject in subjectOptions"
                  :key="subject"
                  :label="subject"
                  :value="subject"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="册别" prop="volume">
              <el-select v-model="form.volume" placeholder="选择册别">
                <el-option
                  v-for="volume in volumeOptions"
                  :key="volume"
                  :label="volume"
                  :value="volume"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="板块" prop="section">
              <el-select v-model="form.section" placeholder="选择板块">
                <el-option
                  v-for="section in sectionOptions"
                  :key="section"
                  :label="section"
                  :value="section"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-select v-model="form.category" placeholder="选择分类">
                <el-option label="常规资料" value="regular" />
                <el-option label="升学专区" value="upgrade" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="广告次数" prop="ad_required_count">
              <el-input-number
                v-model="form.ad_required_count"
                :min="1"
                :max="10"
                placeholder="下载前需观看的广告次数"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序权重" prop="sort_order">
              <el-input-number
                v-model="form.sort_order"
                :min="0"
                :max="9999"
                placeholder="数值越大排序越靠前"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="选择状态">
                <el-option label="活跃" value="active" />
                <el-option label="禁用" value="inactive" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="标签" prop="tags">
              <el-input
                v-model="tagsInput"
                placeholder="请输入标签，多个标签用逗号分隔"
                @blur="handleTagsChange"
              />
              <div v-if="form.tags && form.tags.length > 0" class="tags-display">
                <el-tag
                  v-for="tag in form.tags"
                  :key="tag"
                  closable
                  @close="removeTag(tag)"
                  style="margin-right: 8px; margin-top: 8px;"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="特色功能" prop="features">
              <el-input
                v-model="featuresInput"
                placeholder="请输入特色功能，多个功能用逗号分隔"
                @blur="handleFeaturesChange"
              />
              <div v-if="form.features && form.features.length > 0" class="features-display">
                <el-tag
                  v-for="feature in form.features"
                  :key="feature"
                  type="success"
                  closable
                  @close="removeFeature(feature)"
                  style="margin-right: 8px; margin-top: 8px;"
                >
                  {{ feature }}
                </el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="saving"
          @click="handleSave"
        >
          {{ saving ? '保存中...' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { fileApi } from '@/api/files'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  fileId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const loading = ref(false)
const saving = ref(false)
const tagsInput = ref('')
const featuresInput = ref('')

// 表单数据
const form = reactive({
  title: '',
  description: '',
  grade: '',
  subject: '',
  volume: '',
  section: '',
  category: 'regular',
  tags: [],
  ad_required_count: 1,
  sort_order: 0,
  features: [],
  status: 'active'
})

// 选项数据
const gradeOptions = ref([])
const subjectOptions = ref([])
const volumeOptions = ref([])
const sectionOptions = ref([])

// 表单验证规则
const rules = {
  title: [{ required: true, message: '请输入文件标题', trigger: 'blur' }],
  subject: [{ required: true, message: '请选择科目', trigger: 'change' }],
  volume: [{ required: true, message: '请选择册别', trigger: 'change' }],
  section: [{ required: true, message: '请选择板块', trigger: 'change' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听文件ID变化，异步加载文件数据
watch(() => props.fileId, async (newFileId) => {
  if (newFileId && visible.value) {
    await loadFileData(newFileId)
  }
}, { immediate: true })

// 监听对话框显示状态
watch(visible, async (newVisible) => {
  if (newVisible) {
    await loadFilterOptions()
    if (props.fileId) {
      await loadFileData(props.fileId)
    }
  } else {
    resetForm()
  }
})

// 方法
const loadFilterOptions = async () => {
  try {
    const result = await fileApi.getFilterOptions()
    if (result.success) {
      gradeOptions.value = result.data.grades || []
      subjectOptions.value = result.data.subjects || []
      volumeOptions.value = result.data.volumes || []
      sectionOptions.value = result.data.sections || []
    }
  } catch (error) {
    console.error('加载筛选选项失败:', error)
  }
}

const loadFileData = async (fileId) => {
  if (!fileId) return

  loading.value = true
  try {
    const result = await fileApi.getFileDetail(fileId)
    if (result.success) {
      const fileData = result.data

      // 填充表单数据
      Object.assign(form, {
        title: fileData.title || '',
        description: fileData.description || '',
        grade: fileData.grade || '',
        subject: fileData.subject || '',
        volume: fileData.volume || '',
        section: fileData.section || '',
        category: fileData.category || 'regular',
        tags: fileData.tags || [],
        ad_required_count: fileData.ad_required_count || 1,
        sort_order: fileData.sort_order || 0,
        features: fileData.features || [],
        status: fileData.status || 'active'
      })

      // 更新标签和特色功能的输入框
      tagsInput.value = form.tags.join(', ')
      featuresInput.value = form.features.join(', ')
    } else {
      ElMessage.error('加载文件数据失败: ' + result.error)
    }
  } catch (error) {
    ElMessage.error('加载文件数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleTagsChange = () => {
  if (tagsInput.value.trim()) {
    form.tags = tagsInput.value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag)
  } else {
    form.tags = []
  }
}

const removeTag = (tagToRemove) => {
  form.tags = form.tags.filter(tag => tag !== tagToRemove)
  tagsInput.value = form.tags.join(', ')
}

const handleFeaturesChange = () => {
  if (featuresInput.value.trim()) {
    form.features = featuresInput.value
      .split(',')
      .map(feature => feature.trim())
      .filter(feature => feature)
  } else {
    form.features = []
  }
}

const removeFeature = (featureToRemove) => {
  form.features = form.features.filter(feature => feature !== featureToRemove)
  featuresInput.value = form.features.join(', ')
}

const handleSave = async () => {
  try {
    await formRef.value.validate()

    saving.value = true

    const updateData = {
      title: form.title,
      description: form.description,
      grade: form.grade,
      subject: form.subject,
      volume: form.volume,
      section: form.section,
      category: form.category,
      tags: form.tags,
      ad_required_count: form.ad_required_count,
      sort_order: form.sort_order,
      features: form.features,
      status: form.status
    }

    const result = await fileApi.updateFile(props.fileId, updateData)

    if (result.success) {
      ElMessage.success('文件信息更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error('更新失败: ' + result.error)
    }
  } catch (error) {
    if (error !== 'validation failed') {
      ElMessage.error('更新失败: ' + error.message)
    }
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  Object.assign(form, {
    title: '',
    description: '',
    grade: '',
    subject: '',
    volume: '',
    section: '',
    category: 'regular',
    tags: [],
    ad_required_count: 1,
    sort_order: 0,
    features: [],
    status: 'active'
  })
  tagsInput.value = ''
  featuresInput.value = ''

  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.edit-content {
  min-height: 400px;
}

.tags-display,
.features-display {
  margin-top: 8px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__count) {
  color: #909399;
}
</style>
