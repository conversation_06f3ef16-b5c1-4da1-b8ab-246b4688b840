// 获取用户openid云函数 - 标准方法
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

exports.main = async (event, context) => {
  console.log('云函数被调用，event:', event)
  console.log('云函数被调用，context:', context)

  try {
    // 标准方法：使用 cloud.getWXContext() 获取微信调用上下文
    const wxContext = cloud.getWXContext()

    console.log('wxContext:', wxContext)

    return {
      success: true,
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      unionid: wxContext.UNIONID,
      event: event,
      timestamp: new Date()
    }

  } catch (error) {
    console.error('云函数执行失败:', error)
    return {
      success: false,
      error: 'function_error',
      message: '云函数执行失败',
      details: error.message
    }
  }
}
