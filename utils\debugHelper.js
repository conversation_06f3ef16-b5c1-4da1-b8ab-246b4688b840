// 调试助手工具
class DebugHelper {
  
  // 检查小程序环境
  static checkEnvironment() {
    try {
      const deviceInfo = wx.getDeviceInfo()
      const appInfo = wx.getAppBaseInfo()
      const accountInfo = wx.getAccountInfoSync()

      return {
        // 系统信息
        platform: deviceInfo.platform,
        version: appInfo.version,
        SDKVersion: appInfo.SDKVersion,

        // 小程序信息
        appId: accountInfo.miniProgram.appId,
        envVersion: accountInfo.miniProgram.envVersion, // develop, trial, release

        // 云开发状态
        cloudSupported: !!wx.cloud,

        // 网络状态
        networkType: deviceInfo.networkType || 'unknown'
      }
    } catch (error) {
      // 降级到旧API
      const systemInfo = wx.getSystemInfoSync()
      const accountInfo = wx.getAccountInfoSync()

      return {
        platform: systemInfo.platform,
        version: systemInfo.version,
        SDKVersion: systemInfo.SDKVersion,
        appId: accountInfo.miniProgram.appId,
        envVersion: accountInfo.miniProgram.envVersion,
        cloudSupported: !!wx.cloud,
        networkType: systemInfo.networkType
      }
    }
  }
  
  // 检查云开发状态
  static async checkCloudStatus() {
    try {
      // 测试云数据库连接
      const db = wx.cloud.database()
      const testResult = await db.collection('system_configs').limit(1).get()
      
      return {
        cloudInit: true,
        databaseConnected: true,
        testQuery: testResult.data.length > 0
      }
    } catch (error) {
      return {
        cloudInit: !!wx.cloud,
        databaseConnected: false,
        error: error.message
      }
    }
  }
  
  // 测试云函数调用
  static async testCloudFunction() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getOpenId',
        data: { test: true }
      })
      
      return {
        functionCallable: true,
        result: result.result,
        success: result.result.success
      }
    } catch (error) {
      return {
        functionCallable: false,
        error: error.message
      }
    }
  }
  
  // 检查用户授权状态
  static checkUserAuth() {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          resolve({
            hasUserInfo: res.authSetting['scope.userInfo'],
            hasUserLocation: res.authSetting['scope.userLocation'],
            authSettings: res.authSetting
          })
        },
        fail: (error) => {
          resolve({
            error: error.message
          })
        }
      })
    })
  }
  
  // 尝试获取用户信息
  static getUserProfile() {
    return new Promise((resolve) => {
      wx.getUserProfile({
        desc: '用于调试检查',
        success: (res) => {
          resolve({
            hasUserProfile: true,
            userInfo: res.userInfo
          })
        },
        fail: (error) => {
          resolve({
            hasUserProfile: false,
            error: error.errMsg
          })
        }
      })
    })
  }
  
  // 综合诊断
  static async diagnose() {
    console.log('🔍 开始系统诊断...')
    
    const diagnosis = {
      timestamp: new Date(),
      environment: this.checkEnvironment(),
      cloudStatus: await this.checkCloudStatus(),
      cloudFunction: await this.testCloudFunction(),
      userAuth: await this.checkUserAuth()
    }
    
    console.log('📊 诊断结果:', diagnosis)
    
    // 分析问题
    const issues = []
    const suggestions = []
    
    if (!diagnosis.environment.cloudSupported) {
      issues.push('云开发不支持')
      suggestions.push('请升级基础库版本到2.2.3以上')
    }
    
    if (!diagnosis.cloudStatus.databaseConnected) {
      issues.push('云数据库连接失败')
      suggestions.push('检查云开发环境配置')
    }
    
    if (!diagnosis.cloudFunction.functionCallable) {
      issues.push('云函数调用失败')
      suggestions.push('检查云函数部署状态')
    }
    
    if (!diagnosis.cloudFunction.success) {
      issues.push('云函数返回失败')
      if (diagnosis.environment.envVersion === 'develop') {
        suggestions.push('开发环境可能无法获取真实openid，这是正常现象')
      } else {
        suggestions.push('检查用户登录状态和授权')
      }
    }
    
    diagnosis.issues = issues
    diagnosis.suggestions = suggestions
    
    return diagnosis
  }
  
  // 显示诊断结果
  static async showDiagnosis() {
    wx.showLoading({ title: '诊断中...' })
    
    try {
      const diagnosis = await this.diagnose()
      wx.hideLoading()
      
      let message = '📊 系统诊断结果:\n\n'
      
      // 环境信息
      message += `🔧 环境: ${diagnosis.environment.envVersion}\n`
      message += `📱 平台: ${diagnosis.environment.platform}\n`
      message += `☁️ 云开发: ${diagnosis.cloudStatus.cloudInit ? '✅' : '❌'}\n`
      message += `🗄️ 数据库: ${diagnosis.cloudStatus.databaseConnected ? '✅' : '❌'}\n`
      message += `⚡ 云函数: ${diagnosis.cloudFunction.functionCallable ? '✅' : '❌'}\n\n`
      
      // 问题和建议
      if (diagnosis.issues.length > 0) {
        message += '⚠️ 发现问题:\n'
        diagnosis.issues.forEach(issue => {
          message += `• ${issue}\n`
        })
        message += '\n💡 建议:\n'
        diagnosis.suggestions.forEach(suggestion => {
          message += `• ${suggestion}\n`
        })
      } else {
        message += '✅ 系统运行正常'
      }
      
      wx.showModal({
        title: '系统诊断',
        content: message,
        showCancel: false,
        confirmText: '知道了'
      })
      
      return diagnosis
      
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '诊断失败',
        icon: 'none'
      })
      console.error('诊断失败:', error)
    }
  }
}

module.exports = {
  DebugHelper
}
