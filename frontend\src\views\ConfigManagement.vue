<template>
  <div class="config-management">
    <!-- 页面头部 -->
    <el-card class="header-card">
      <div class="header-content">
        <div>
          <h2>🔧 系统配置管理</h2>
          <p>管理小程序的系统参数、广告配置等设置</p>
        </div>
        <div class="header-actions">
          <el-button @click="loadConfigs" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <div class="save-status" v-if="savingKeys.length > 0">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>正在保存...</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 配置内容 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <div v-else class="config-tabs">
      <el-tabs v-model="activeTab" type="border-card" class="config-tabs-container">
        <!-- 基础配置 -->
        <el-tab-pane label="📱 基础配置" name="general">
          <div class="tab-content">
            <div class="tab-header">
              <h3>基础配置</h3>
              <p>应用名称、版本等基础信息配置</p>
            </div>
            <div class="config-items">
              <div
                v-for="config in configData.general || []"
                :key="config.key"
                class="config-item"
              >
                <div class="config-info">
                  <div class="config-label">
                    <span class="config-title">{{ config.description || config.key }}</span>
                    <div class="config-meta">
                      <span class="config-key">{{ config.key }}</span>
                      <el-tag size="small" :type="getTypeColor(config.type)">
                        {{ config.type }}
                      </el-tag>
                    </div>
                  </div>
                </div>

                <div class="config-value">
                  <!-- 字符串类型 -->
                  <div v-if="config.type === 'string'" class="input-wrapper">
                    <el-input
                      v-model="config.value"
                      :placeholder="`请输入${config.description || config.key}`"
                      @blur="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- 数字类型 -->
                  <div v-else-if="config.type === 'number'" class="input-wrapper">
                    <el-input-number
                      v-model="config.value"
                      :min="0"
                      @blur="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- 布尔类型 -->
                  <div v-else-if="config.type === 'boolean'" class="input-wrapper">
                    <el-switch
                      v-model="config.value"
                      @change="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- JSON类型 -->
                  <div v-else-if="config.type === 'json'" class="input-wrapper">
                    <el-input
                      v-model="jsonValues[config.key]"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入有效的JSON格式"
                      @blur="handleJsonBlur(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- 其他类型 -->
                  <div v-else class="input-wrapper">
                    <el-input
                      v-model="config.value"
                      @blur="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 搜索配置 -->
        <el-tab-pane label="🔍 搜索配置" name="search">
          <div class="tab-content">
            <div class="tab-header">
              <h3>搜索配置</h3>
              <p>热搜关键词、搜索功能相关配置</p>
            </div>
            <div class="config-items">
              <div
                v-for="config in configData.search || []"
                :key="config.key"
                class="config-item"
              >
                <div class="config-info">
                  <div class="config-label">
                    <span class="config-title">{{ config.description || config.key }}</span>
                    <div class="config-meta">
                      <span class="config-key">{{ config.key }}</span>
                      <el-tag size="small" :type="getTypeColor(config.type)">
                        {{ config.type }}
                      </el-tag>
                    </div>
                  </div>
                </div>

                <div class="config-value">
                  <!-- 字符串类型 -->
                  <div v-if="config.type === 'string'" class="input-wrapper">
                    <el-input
                      v-model="config.value"
                      :placeholder="`请输入${config.description || config.key}`"
                      @blur="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- 数字类型 -->
                  <div v-else-if="config.type === 'number'" class="input-wrapper">
                    <el-input-number
                      v-model="config.value"
                      :min="0"
                      @blur="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- 布尔类型 -->
                  <div v-else-if="config.type === 'boolean'" class="input-wrapper">
                    <el-switch
                      v-model="config.value"
                      @change="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- JSON类型 -->
                  <div v-else-if="config.type === 'json'" class="input-wrapper">
                    <el-input
                      v-model="jsonValues[config.key]"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入有效的JSON格式"
                      @blur="handleJsonBlur(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- 其他类型 -->
                  <div v-else class="input-wrapper">
                    <el-input
                      v-model="config.value"
                      @blur="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 下载配置 -->
        <el-tab-pane label="⬇️ 下载配置" name="download">
          <div class="tab-content">
            <div class="tab-header">
              <h3>下载配置</h3>
              <p>下载频率限制、安全检测等下载相关配置</p>
            </div>
            <div class="config-items">
              <div
                v-for="config in configData.download || []"
                :key="config.key"
                class="config-item"
              >
                <div class="config-info">
                  <div class="config-label">
                    <span class="config-title">{{ config.description || config.key }}</span>
                    <div class="config-meta">
                      <span class="config-key">{{ config.key }}</span>
                      <el-tag size="small" :type="getTypeColor(config.type)">
                        {{ config.type }}
                      </el-tag>
                    </div>
                  </div>
                </div>

                <div class="config-value">
                  <!-- 字符串类型 -->
                  <div v-if="config.type === 'string'" class="input-wrapper">
                    <el-input
                      v-model="config.value"
                      :placeholder="`请输入${config.description || config.key}`"
                      @blur="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- 数字类型 -->
                  <div v-else-if="config.type === 'number'" class="input-wrapper">
                    <el-input-number
                      v-model="config.value"
                      :min="0"
                      @blur="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- 布尔类型 -->
                  <div v-else-if="config.type === 'boolean'" class="input-wrapper">
                    <el-switch
                      v-model="config.value"
                      @change="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- JSON类型 -->
                  <div v-else-if="config.type === 'json'" class="input-wrapper">
                    <el-input
                      v-model="jsonValues[config.key]"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入有效的JSON格式"
                      @blur="handleJsonBlur(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- 其他类型 -->
                  <div v-else class="input-wrapper">
                    <el-input
                      v-model="config.value"
                      @blur="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 广告配置 -->
        <el-tab-pane label="📺 广告配置" name="ads">
          <div class="tab-content">
            <div class="tab-header">
              <h3>广告配置</h3>
              <p>广告位设置、广告商配置等广告相关设置</p>
            </div>
            <div class="config-items">
              <div
                v-for="config in configData.ads || []"
                :key="config.key"
                class="config-item"
              >
                <div class="config-info">
                  <div class="config-label">
                    <span class="config-title">{{ config.description || config.key }}</span>
                    <div class="config-meta">
                      <span class="config-key">{{ config.key }}</span>
                      <el-tag size="small" :type="getTypeColor(config.type)">
                        {{ config.type }}
                      </el-tag>
                    </div>
                  </div>
                </div>

                <div class="config-value">
                  <!-- 字符串类型 -->
                  <div v-if="config.type === 'string'" class="input-wrapper">
                    <el-input
                      v-model="config.value"
                      :placeholder="`请输入${config.description || config.key}`"
                      @blur="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- 数字类型 -->
                  <div v-else-if="config.type === 'number'" class="input-wrapper">
                    <el-input-number
                      v-model="config.value"
                      :min="0"
                      @blur="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- 布尔类型 -->
                  <div v-else-if="config.type === 'boolean'" class="input-wrapper">
                    <el-switch
                      v-model="config.value"
                      @change="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- JSON类型 -->
                  <div v-else-if="config.type === 'json'" class="input-wrapper">
                    <el-input
                      v-model="jsonValues[config.key]"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入有效的JSON格式"
                      @blur="handleJsonBlur(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>

                  <!-- 其他类型 -->
                  <div v-else class="input-wrapper">
                    <el-input
                      v-model="config.value"
                      @blur="autoSaveConfig(config)"
                      :loading="savingKeys.includes(config.key)"
                    />
                    <div class="save-indicator" v-if="savingKeys.includes(config.key)">
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { configAPI } from '@/api/config'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const configData = ref({})
const originalData = ref({})
const changedKeys = ref([])
const savingKeys = ref([])
const jsonValues = reactive({})
const activeTab = ref('general')

// 防抖定时器
const saveTimers = new Map()

// 分类标题映射
const categoryTitles = {
  general: '📱 基础配置',
  search: '🔍 搜索配置',
  download: '⬇️ 下载配置',
  ads: '📺 广告配置'
}

// 获取分类标题
const getCategoryTitle = (category) => {
  return categoryTitles[category] || `📁 ${category}`
}

// 获取类型颜色
const getTypeColor = (type) => {
  const colors = {
    string: '',
    number: 'success',
    boolean: 'warning',
    json: 'info'
  }
  return colors[type] || ''
}

// 加载配置数据
const loadConfigs = async () => {
  loading.value = true
  try {
    const response = await configAPI.getAllConfigs()
    if (response.success) {
      configData.value = response.data
      originalData.value = JSON.parse(JSON.stringify(response.data))

      // 初始化JSON值
      Object.values(response.data).flat().forEach(config => {
        if (config.type === 'json') {
          jsonValues[config.key] = JSON.stringify(config.value, null, 2)
        }
      })

      changedKeys.value = []
      ElMessage.success('配置数据加载成功')
    } else {
      ElMessage.error('加载配置失败: ' + response.error)
    }
  } catch (error) {
    ElMessage.error('加载配置失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 自动保存配置（带防抖）
const autoSaveConfig = async (config) => {
  const key = config.key

  // 清除之前的定时器
  if (saveTimers.has(key)) {
    clearTimeout(saveTimers.get(key))
  }

  // 设置新的防抖定时器
  const timer = setTimeout(async () => {
    // 检查值是否真的改变了
    const originalConfig = findOriginalConfig(key)
    if (originalConfig && originalConfig.value === config.value) {
      return // 值没有改变，不需要保存
    }

    // 添加到保存队列
    if (!savingKeys.value.includes(key)) {
      savingKeys.value.push(key)
    }

    try {
      const response = await configAPI.updateConfig(key, config.value, config.type)
      if (response.success) {
        // 静默保存成功，更新原始数据
        updateOriginalData(key, config.value)
        // 可选：显示轻量级成功提示
        ElMessage({
          message: `${config.description || key} 已保存`,
          type: 'success',
          duration: 1500,
          showClose: false
        })
      } else {
        ElMessage.error(`保存失败: ${response.error}`)
        // 恢复原始值
        const original = findOriginalConfig(key)
        if (original) {
          config.value = original.value
        }
      }
    } catch (error) {
      ElMessage.error(`保存失败: ${error.message}`)
      // 恢复原始值
      const original = findOriginalConfig(key)
      if (original) {
        config.value = original.value
      }
    } finally {
      // 从保存队列中移除
      const index = savingKeys.value.indexOf(key)
      if (index > -1) {
        savingKeys.value.splice(index, 1)
      }
      saveTimers.delete(key)
    }
  }, 800) // 800ms 防抖延迟

  saveTimers.set(key, timer)
}

// 处理JSON失焦保存
const handleJsonBlur = (config) => {
  try {
    const parsed = JSON.parse(jsonValues[config.key])
    config.value = parsed
    autoSaveConfig(config)
  } catch (error) {
    ElMessage.warning('JSON格式不正确，请检查语法')
    // 恢复原始JSON值
    const original = findOriginalConfig(config.key)
    if (original) {
      jsonValues[config.key] = JSON.stringify(original.value, null, 2)
    }
  }
}

// 重置配置
const resetConfig = (config) => {
  // 从原始数据中恢复
  const originalConfig = findOriginalConfig(config.key)
  if (originalConfig) {
    config.value = originalConfig.value
    if (config.type === 'json') {
      jsonValues[config.key] = JSON.stringify(originalConfig.value, null, 2)
    }
    // 从更改列表中移除
    const index = changedKeys.value.indexOf(config.key)
    if (index > -1) {
      changedKeys.value.splice(index, 1)
    }
  }
}

// 查找原始配置
const findOriginalConfig = (key) => {
  for (const configs of Object.values(originalData.value)) {
    const config = configs.find(c => c.key === key)
    if (config) return config
  }
  return null
}

// 更新原始数据
const updateOriginalData = (key, value) => {
  for (const configs of Object.values(originalData.value)) {
    const config = configs.find(c => c.key === key)
    if (config) {
      config.value = value
      break
    }
  }
}

// 清理所有防抖定时器
const clearAllTimers = () => {
  saveTimers.forEach(timer => clearTimeout(timer))
  saveTimers.clear()
}

// 组件挂载时加载数据
onMounted(() => {
  loadConfigs()
})

onUnmounted(() => {
  clearAllTimers()
})
</script>

<style scoped>
.config-management {
  max-width: 1200px;
  margin: 0 auto;
}

.header-card {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #909399;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.loading-container {
  padding: 20px;
}

.config-tabs {
  margin-top: 20px;
}

.config-tabs-container {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
  padding: 20px;
}

.tab-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.tab-header h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.tab-header p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.config-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.config-info {
  flex: 1;
  min-width: 200px;
}

.config-label {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.config-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.config-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-key {
  font-size: 12px;
  color: #909399;
  font-family: 'Courier New', monospace;
}

.config-value {
  flex: 2;
  min-width: 300px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
}

.save-indicator {
  display: flex;
  align-items: center;
  color: #409eff;
  font-size: 14px;
}

.save-status {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #409eff;
  font-size: 14px;
}

.floating-actions {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-item {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .config-info,
  .config-value {
    min-width: auto;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
}
</style>
