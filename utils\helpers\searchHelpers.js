/**
 * 搜索页专用数据处理工具
 * 严格按照项目解耦规则：仅供搜索页使用，其他页面禁止调用
 */

/**
 * 处理搜索结果数据
 */
const processSearchResultData = (rawData) => {
  return rawData.map(item => ({
    _id: item._id,
    title: item.title,
    tags: generateSearchTags(item),
    download_count: item.download_count || 0,
    view_count: item.view_count || 0,
    share_count: item.share_count || 0,
    fileInfo: {
      type: (item.file_type || 'PDF').toUpperCase(),
      pages: item.pages || 0,
      size: formatSearchFileSize(item.file_size),
      features: Array.isArray(item.features) ? item.features.slice(0, 3) : []
    },
    subject: item.subject || '',
    grade: item.grade || '',
    volume: item.volume || '',
    section: item.section || '',
    sortOrder: item.sort_order || 0,
    createdTime: item.created_time
  }))
}

/**
 * 生成搜索结果标签
 */
const generateSearchTags = (item) => {
  // 直接使用数据库中的 tags 字段
  return Array.isArray(item.tags) ? item.tags.slice(0, 4) : []
}

/**
 * 格式化搜索页文件大小
 */
const formatSearchFileSize = (size) => {
  if (!size || size <= 0) return ''
  
  if (size >= 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(1) + 'MB'
  } else if (size >= 1024) {
    return (size / 1024).toFixed(1) + 'KB'
  } else {
    return size + 'B'
  }
}

/**
 * 生成搜索页文件信息显示文本
 */
const generateSearchFileInfo = (file) => {
  const parts = []
  
  // 文件类型
  if (file.fileInfo.type) {
    parts.push(file.fileInfo.type)
  }
  
  // 页数信息
  if (file.fileInfo.pages > 0) {
    parts.push(`${file.fileInfo.pages}页`)
  }
  
  // 文件大小
  if (file.fileInfo.size) {
    parts.push(file.fileInfo.size)
  }
  
  // 特性信息（最多显示2个）
  if (file.fileInfo.features && file.fileInfo.features.length > 0) {
    parts.push(...file.fileInfo.features.slice(0, 2))
  }
  
  return parts.join('·')
}

/**
 * 获取搜索排序选项
 */
const getSearchSortOptions = () => {
  return [
    {
      value: 'sort_order',
      text: '推荐排序',
      icon: '⭐',
      defaultOrder: 'desc'
    },
    {
      value: 'download_count',
      text: '下载量',
      icon: '⬇️',
      defaultOrder: 'desc'
    },
    {
      value: 'view_count',
      text: '查看量',
      icon: '👁️',
      defaultOrder: 'desc'
    },
    {
      value: 'created_time',
      text: '最新上传',
      icon: '🕒',
      defaultOrder: 'desc'
    }
  ]
}

/**
 * 获取排序显示文本
 */
const getSortDisplayText = (sortType) => {
  const sortOptions = getSearchSortOptions()
  const option = sortOptions.find(opt => opt.value === sortType)
  return option ? option.text : '推荐排序'
}

/**
 * 处理搜索历史
 */
const processSearchHistory = (history, maxCount = 10) => {
  if (!Array.isArray(history)) return []
  
  return history
    .filter(item => item && typeof item === 'string' && item.trim())
    .map(item => item.trim())
    .slice(0, maxCount)
}

/**
 * 保存搜索历史
 */
const saveSearchHistory = (keyword, maxCount = 20) => {
  try {
    if (!keyword || !keyword.trim()) return
    
    const trimmedKeyword = keyword.trim()
    let history = wx.getStorageSync('searchHistory') || []
    
    // 移除重复项
    history = history.filter(item => item !== trimmedKeyword)
    
    // 添加到开头
    history.unshift(trimmedKeyword)
    
    // 限制历史记录数量
    if (history.length > maxCount) {
      history = history.slice(0, maxCount)
    }
    
    wx.setStorageSync('searchHistory', history)
    return history
    
  } catch (error) {
    console.error('保存搜索历史失败:', error)
    return []
  }
}

/**
 * 获取搜索历史
 */
const getSearchHistory = (maxCount = 10) => {
  try {
    const history = wx.getStorageSync('searchHistory') || []
    return processSearchHistory(history, maxCount)
  } catch (error) {
    console.error('获取搜索历史失败:', error)
    return []
  }
}

/**
 * 清除搜索历史
 */
const clearSearchHistory = () => {
  try {
    wx.removeStorageSync('searchHistory')
    return true
  } catch (error) {
    console.error('清除搜索历史失败:', error)
    return false
  }
}

/**
 * 高亮搜索关键词
 */
const highlightSearchKeyword = (text, keyword) => {
  if (!keyword || !text) return text
  
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<span class="highlight">$1</span>')
}

/**
 * 分析搜索关键词
 */
const analyzeSearchKeyword = (keyword) => {
  if (!keyword || !keyword.trim()) {
    return {
      originalKeyword: '',
      keywords: [],
      hasGrade: false,
      hasSubject: false,
      gradeKeywords: [],
      subjectKeywords: [],
      searchTips: ''
    }
  }

  const trimmedKeyword = keyword.trim()
  const keywords = trimmedKeyword.split(/\s+/).filter(k => k.length > 0)
  
  // 年级关键词
  const gradePatterns = ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '幼升小', '小升初']
  const gradeKeywords = keywords.filter(k => 
    gradePatterns.some(pattern => k.includes(pattern) || pattern.includes(k))
  )
  
  // 学科关键词
  const subjectPatterns = ['语文', '数学', '英语', '科学', '道德与法治', '音乐', '美术', '体育']
  const subjectKeywords = keywords.filter(k => 
    subjectPatterns.some(pattern => k.includes(pattern) || pattern.includes(k))
  )

  // 生成搜索提示
  let searchTips = ''
  if (gradeKeywords.length > 0 && subjectKeywords.length > 0) {
    searchTips = `精确搜索：${gradeKeywords[0]} ${subjectKeywords[0]}`
  } else if (keywords.length > 1) {
    searchTips = `多词搜索：优先显示包含更多关键词的结果`
  }

  return {
    originalKeyword: trimmedKeyword,
    keywords: keywords,
    hasGrade: gradeKeywords.length > 0,
    hasSubject: subjectKeywords.length > 0,
    gradeKeywords: gradeKeywords,
    subjectKeywords: subjectKeywords,
    searchTips: searchTips
  }
}

/**
 * 生成搜索建议关键词
 */
const generateSearchSuggestions = (keyword, hotKeywords = []) => {
  if (!keyword || keyword.length < 2) return []
  
  const keywordLower = keyword.toLowerCase()
  const suggestions = []
  
  // 从热门关键词中匹配
  hotKeywords.forEach(hotKeyword => {
    if (hotKeyword.toLowerCase().includes(keywordLower)) {
      suggestions.push(hotKeyword)
    }
  })
  
  // 智能组合建议
  const gradePatterns = ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级']
  const subjectPatterns = ['语文', '数学', '英语', '科学']
  const typePatterns = ['试卷', '练习册', '知识点', '单元测试']
  
  // 如果输入了年级，建议加上学科
  gradePatterns.forEach(grade => {
    if (keywordLower.includes(grade.toLowerCase())) {
      subjectPatterns.forEach(subject => {
        if (!keywordLower.includes(subject.toLowerCase())) {
          suggestions.push(`${grade}${subject}`)
        }
      })
    }
  })
  
  // 如果输入了学科，建议加上年级
  subjectPatterns.forEach(subject => {
    if (keywordLower.includes(subject.toLowerCase())) {
      gradePatterns.forEach(grade => {
        if (!keywordLower.includes(grade.toLowerCase())) {
          suggestions.push(`${grade}${subject}`)
        }
      })
    }
  })
  
  return [...new Set(suggestions)].slice(0, 8) // 去重并限制数量
}

/**
 * 格式化搜索结果统计信息
 */
const formatSearchResultStats = (total, keyword) => {
  if (total === 0) {
    return `未找到"${keyword}"相关的资料`
  } else if (total === 1) {
    return `找到 1 个相关资料`
  } else {
    return `找到 ${total} 个相关资料`
  }
}

/**
 * 检查搜索关键词是否有效
 */
const isValidSearchKeyword = (keyword) => {
  if (!keyword || typeof keyword !== 'string') return false
  
  const trimmed = keyword.trim()
  if (trimmed.length === 0) return false
  if (trimmed.length > 50) return false // 限制最大长度
  
  // 检查是否包含有效字符
  const validPattern = /[\u4e00-\u9fa5a-zA-Z0-9]/
  return validPattern.test(trimmed)
}

module.exports = {
  processSearchResultData,
  generateSearchTags,
  formatSearchFileSize,
  generateSearchFileInfo,
  getSearchSortOptions,
  getSortDisplayText,
  processSearchHistory,
  saveSearchHistory,
  getSearchHistory,
  clearSearchHistory,
  highlightSearchKeyword,
  analyzeSearchKeyword,
  generateSearchSuggestions,
  formatSearchResultStats,
  isValidSearchKeyword
}