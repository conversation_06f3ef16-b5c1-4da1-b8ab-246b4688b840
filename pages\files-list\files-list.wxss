.container {
  background: #F8F9FA;
  min-height: 100vh;
  animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 顶部导航 */
.header {
  background: #1677FF;
  padding-top: 88rpx;
  animation: slideInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 40rpx;
  color: white;
}

.back-btn, .search-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.back-btn::after, .search-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
}

.back-btn:active, .search-btn:active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(0.95);
}

.back-btn:active::after, .search-btn:active::after {
  width: 120rpx;
  height: 120rpx;
}

.back-icon, .search-icon {
  font-size: 36rpx;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  flex: 1;
  text-align: center;
  color: white;
}

/* 筛选器区域 */
.filter-section {
  background: white;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  border-radius: 0 0 32rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(22, 119, 255, 0.08);
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-row {
  display: flex;
  gap: 16rpx;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 20rpx;
  background: #F8F9FA;
  border-radius: 20rpx;
  border: 2rpx solid #E8EAED;
  min-height: 80rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.filter-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.15) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
}

.filter-item:active {
  background: #E6F4FF;
  border-color: #1677FF;
  transform: scale(0.98);
}

.filter-item:active::after {
  width: 200rpx;
  height: 200rpx;
}

/* 排序工具样式 */
.sort-section {
  margin-top: 24rpx;
  display: flex;
  justify-content: flex-start;
  gap: 32rpx;
  padding: 0 8rpx;
}

.sort-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 24rpx;
  background: transparent;
  border: none;
  color: #666;
  font-size: 28rpx;
  position: relative;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.sort-item.active {
  color: #1677FF;
  background: #E6F4FF;
}

.sort-item.active::after {
  content: '';
  position: absolute;
  bottom: -4rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 32rpx;
  height: 4rpx;
  background: #1677FF;
  border-radius: 2rpx;
}

.sort-text {
  margin-right: 8rpx;
}

.sort-arrow {
  font-size: 20rpx;
  color: #1677FF;
  margin-left: 8rpx;
  cursor: pointer;
}

.filter-text {
  font-size: 28rpx;
  color: #333;
  margin-right: 8rpx;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.filter-arrow {
  font-size: 20rpx;
  color: #666;
  transition: transform 0.3s ease;
  position: relative;
  z-index: 1;
}

/* 已选筛选条件 */
.selected-filters {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-top: 24rpx;
  flex-wrap: wrap;
}

.filter-tag {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: #E6F4FF;
  border: 2rpx solid #1677FF;
  border-radius: 20rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.filter-tag::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
}

.filter-tag:active {
  background: #D6E4FF;
  transform: scale(0.95);
}

.filter-tag:active::after {
  width: 120rpx;
  height: 120rpx;
}

.tag-text {
  font-size: 24rpx;
  color: #1677FF;
  margin-right: 8rpx;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.tag-close {
  font-size: 24rpx;
  color: #1677FF;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.clear-all {
  padding: 12rpx 20rpx;
  background: #FFF3E0;
  border: 2rpx solid #FF9800;
  border-radius: 20rpx;
  color: #FF9800;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.clear-all::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 152, 0, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
}

.clear-all:active {
  background: #FFE0B2;
  transform: scale(0.95);
}

.clear-all:active::after {
  width: 120rpx;
  height: 120rpx;
}

/* 资料列表 */
.material-list {
  padding: 0 24rpx;
}

.list-header {
  padding: 24rpx 16rpx;
}

.result-count {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.material-item {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx 28rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(22, 119, 255, 0.08);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2rpx solid rgba(22, 119, 255, 0.06);
  position: relative;
  overflow: hidden;
  animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateX(-40rpx);
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 错位出现动画 */
.material-item:nth-child(1) { animation-delay: 0.3s; }
.material-item:nth-child(2) { animation-delay: 0.4s; }
.material-item:nth-child(3) { animation-delay: 0.5s; }
.material-item:nth-child(4) { animation-delay: 0.6s; }
.material-item:nth-child(5) { animation-delay: 0.7s; }
.material-item:nth-child(6) { animation-delay: 0.8s; }

.material-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: #1677FF;
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.material-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.8s ease, height 0.8s ease;
  pointer-events: none;
  z-index: 0;
}

.material-item:active {
  transform: translateY(-4rpx) scale(0.995);
  box-shadow: 0 16rpx 60rpx rgba(22, 119, 255, 0.12);
  border-color: #E6F4FF;
}

.material-item:active::before {
  transform: scaleY(1);
}

.material-item:active::after {
  width: 400rpx;
  height: 400rpx;
}

/* 第一行：标题 */
.title-row {
  display: flex;
  align-items: flex-start;
}

.material-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.5;
  flex: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  transition: color 0.3s ease;
  position: relative;
  z-index: 1;
}

.material-item:active .material-title {
  color: #1677FF;
}

/* 第二行：文件信息行 */
.file-info-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap;
}

.file-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
  position: relative;
  z-index: 1;
  flex-shrink: 0;
}

.file-pages, .file-size {
  font-size: 24rpx;
  color: #666666;
  background: #F5F5F5;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.feature-tags-inline {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.feature-tag-small {
  font-size: 22rpx;
  color: #FF9800;
  background: #FFF3E0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid #FFE0B2;
  font-weight: 500;
  position: relative;
  z-index: 1;
  transition: all 0.2s ease;
}

.feature-tag-small:active {
  transform: scale(0.95);
  background: #FFE0B2;
}

/* 第三行：标签 */
.tags-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.category-tag {
  background: #E6F4FF;
  color: #1677FF;
  padding: 10rpx 20rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: 1rpx solid #1677FF;
  position: relative;
  z-index: 1;
  transition: all 0.2s ease;
}

.category-tag:active {
  transform: scale(0.95);
  background: #D6E4FF;
}

/* 第四行：统计数据 */
.stats-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8rpx;
  border-top: 1rpx solid #F0F0F0;
}

.material-stats {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #999999;
  font-weight: 500;
  position: relative;
  z-index: 1;
  transition: color 0.3s ease;
}

.material-item:active .stat-item {
  color: #1677FF;
}

.material-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF9800;
  position: relative;
  z-index: 1;
}

/* 保留原有的其他样式 */
.tag {
  padding: 10rpx 20rpx;
  background: #E6F4FF;
  color: #1677FF;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 16rpx;
  border: 1rpx solid #1677FF;
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 6rpx solid #F0F0F0;
  border-top: 6rpx solid #1677FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
}

/* 空状态 */
.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #666666;
  text-align: center;
  line-height: 1.5;
}

/* 筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  width: 100%;
  max-height: 60vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  border-bottom: 2rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666666;
  border-radius: 32rpx;
  background: #F5F5F5;
  transition: all 0.3s ease;
}

.modal-close:active {
  background: #E8E8E8;
  transform: scale(0.95);
}

.modal-body {
  max-height: calc(60vh - 160rpx);
  overflow-y: auto;
}

.filter-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #F0F0F0;
  transition: all 0.3s ease;
}

.filter-option:last-child {
  border-bottom: none;
}

.filter-option:active {
  background: #F8F9FA;
}

.option-text {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}

.option-text.selected {
  color: #1677FF;
  font-weight: 600;
}

.option-check {
  font-size: 32rpx;
  color: #1677FF;
  font-weight: 600;
}

/* 响应式适配 */
@media (prefers-reduced-motion: reduce) {
  .container,
  .header,
  .filter-section,
  .material-item {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}