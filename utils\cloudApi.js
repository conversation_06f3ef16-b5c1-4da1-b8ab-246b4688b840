// 云数据库API
const cloudApi = {
  // 获取文件列表
  async getFilesList(params = {}) {
    try {
      const db = wx.cloud.database()
      const collection = db.collection('files')
      
      // 构建查询条件
      let whereCondition = {
        status: 'active'
      }
      
      // 根据年级筛选（硬编码年级参数）
      if (params.grade) {
        whereCondition.grade = params.grade
      }
      
      // 根据科目筛选
      if (params.subject) {
        whereCondition.subject = params.subject
      }
      
      // 根据册别筛选
      if (params.volume) {
        whereCondition.volume = params.volume
      }
      
      // 根据板块筛选
      if (params.section) {
        whereCondition.section = params.section
      }
      
      // 先获取总数
      const countResult = await collection.where(whereCondition).count()
      const totalCount = countResult.total
      
      let query = collection.where(whereCondition)
      
      // 排序
      const sortBy = params.sortBy || 'sort_order'
      const sortOrder = params.sortOrder || 'desc'
      
      if (sortOrder === 'desc') {
        query = query.orderBy(sortBy, 'desc')
      } else {
        query = query.orderBy(sortBy, 'asc')
      }
      
      // 如果不是按排序权重排序，则添加排序权重作为次要排序
      if (sortBy !== 'sort_order') {
        query = query.orderBy('sort_order', 'desc')
      }
      
      // 分页
      const page = params.page || 1
      const pageSize = params.pageSize || 20
      const skip = (page - 1) * pageSize
      
      query = query.skip(skip).limit(pageSize)
      
      const result = await query.get()
      
      // 计算是否还有更多数据
      const hasMore = skip + result.data.length < totalCount
      
      return {
        success: true,
        data: result.data,
        total: totalCount,
        hasMore: hasMore,
        currentPage: page,
        pageSize: pageSize
      }
      
    } catch (error) {
      console.error('获取文件列表失败:', error)
      return {
        success: false,
        error: error.message,
        data: [],
        total: 0,
        hasMore: false
      }
    }
  },

  // 获取热门文件
  async getHotFiles(params = {}) {
    try {
      const db = wx.cloud.database()
      const collection = db.collection('files')
      
      const pageSize = params.pageSize || 8
      
      // 优化的热门文件查询逻辑
      const result = await collection
        .where({
          status: 'active'
        })
        .orderBy('sort_order', 'desc')      // 1. 管理员置顶权重
        .orderBy('download_count', 'desc')  // 2. 下载量
        .orderBy('view_count', 'desc')      // 3. 查看量
        .orderBy('created_time', 'desc')    // 4. 创建时间（新文件优先）
        .limit(pageSize)
        .get()
      
      return {
        success: true,
        data: result.data
      }
      
    } catch (error) {
      console.error('获取热门文件失败:', error)
      return {
        success: false,
        error: error.message,
        data: []
      }
    }
  },

  // 获取筛选选项
  async getFilterOptions(grade = null) {
    try {
      const db = wx.cloud.database()
      const collection = db.collection('files')
      
      // 构建基础查询条件
      let whereCondition = {
        status: 'active'
      }
      
      // 根据年级筛选
      if (grade) {
        whereCondition.grade = grade
      }
      
      const result = await collection.where(whereCondition).get()
      
      // 从查询结果中提取唯一的筛选选项
      const subjects = [...new Set(result.data.map(item => item.subject).filter(Boolean))]
      const volumes = [...new Set(result.data.map(item => item.volume).filter(Boolean))]
      const sections = [...new Set(result.data.map(item => item.section).filter(Boolean))]
      
      // 按优先级排序
      const subjectOrder = ['语文', '数学', '英语', '科学', '道德与法治']
      const volumeOrder = ['上册', '下册', '全册']
      
      subjects.sort((a, b) => {
        const aIndex = subjectOrder.indexOf(a)
        const bIndex = subjectOrder.indexOf(b)
        if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex
        if (aIndex !== -1) return -1
        if (bIndex !== -1) return 1
        return a.localeCompare(b)
      })
      
      volumes.sort((a, b) => {
        const aIndex = volumeOrder.indexOf(a)
        const bIndex = volumeOrder.indexOf(b)
        if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex
        if (aIndex !== -1) return -1
        if (bIndex !== -1) return 1
        return a.localeCompare(b)
      })
      
      return {
        success: true,
        data: {
          subjects,
          volumes,
          sections
        }
      }
      
    } catch (error) {
      console.error('获取筛选选项失败:', error)
      return {
        success: false,
        error: error.message,
        data: {
          subjects: [],
          volumes: [],
          sections: []
        }
      }
    }
  },

  // 获取文件详情
  async getFileDetail(id) {
    try {
      const db = wx.cloud.database()
      const result = await db.collection('files').doc(id).get()

      if (result.data) {
        // 通过云函数更新查看次数
        this.updateViewCount(id)

        return {
          success: true,
          data: result.data
        }
      } else {
        return {
          success: false,
          error: '文件不存在'
        }
      }

    } catch (error) {
      console.error('获取文件详情失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  },

  // 更新浏览次数（通过云函数）
  async updateViewCount(id) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'updateViewCount',
        data: { file_id: id }
      })

      if (result.result && result.result.success) {
        console.log('浏览量更新成功')
      } else {
        console.warn('浏览量更新失败:', result.result?.message)
      }
    } catch (error) {
      console.error('更新浏览量失败:', error)
      // 不影响主流程，只记录错误
    }
  },

  // 更新下载次数
  async updateDownloadCount(id) {
    try {
      const db = wx.cloud.database()
      await db.collection('files').doc(id).update({
        data: {
          download_count: db.command.inc(1)
        }
      })
      
      return {
        success: true
      }
      
    } catch (error) {
      console.error('更新下载次数失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  },

  // 搜索文件
  async searchFiles(params = {}) {
    try {
      const db = wx.cloud.database()
      const collection = db.collection('files')
      
      let whereCondition = {
        status: 'active'
      }
      
      // 关键词搜索
      if (params.keyword) {
        whereCondition.title = db.RegExp({
          regexp: params.keyword,
          options: 'i'
        })
      }
      
      // 其他筛选条件
      if (params.grade) whereCondition.grade = params.grade
      if (params.subject) whereCondition.subject = params.subject
      if (params.volume) whereCondition.volume = params.volume
      if (params.section) whereCondition.section = params.section
      
      let query = collection.where(whereCondition)
      
      // 排序
      const sortBy = params.sortBy || 'sort_order'
      const sortOrder = params.sortOrder || 'desc'
      
      if (sortOrder === 'desc') {
        query = query.orderBy(sortBy, 'desc')
      } else {
        query = query.orderBy(sortBy, 'asc')
      }
      
      // 分页
      const page = params.page || 1
      const pageSize = params.pageSize || 20
      const skip = (page - 1) * pageSize
      
      query = query.skip(skip).limit(pageSize)
      
      const result = await query.get()
      
      return {
        success: true,
        data: result.data,
        total: result.data.length,
        hasMore: result.data.length === pageSize
      }
      
    } catch (error) {
      console.error('搜索文件失败:', error)
      return {
        success: false,
        error: error.message,
        data: [],
        total: 0,
        hasMore: false
      }
    }
  },

  // 测试云存储文件访问
  async testCloudFileAccess(fileUrl) {
    try {
      console.log('测试云存储文件访问:', fileUrl)

      const result = await new Promise((resolve, reject) => {
        wx.cloud.getTempFileURL({
          fileList: [fileUrl],
          success: (res) => {
            console.log('getTempFileURL 测试结果:', res)
            resolve(res)
          },
          fail: (error) => {
            console.error('getTempFileURL 测试失败:', error)
            reject(error)
          }
        })
      })

      return {
        success: true,
        data: result
      }

    } catch (error) {
      console.error('测试云存储文件访问失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
}

module.exports = {
  cloudApi
}