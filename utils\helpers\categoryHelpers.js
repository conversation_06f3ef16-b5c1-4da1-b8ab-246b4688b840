/**
 * 分类页专用数据处理工具
 * 严格按照项目解耦规则：仅供分类页使用，其他页面禁止调用
 */

/**
 * 处理分类页文件数据
 */
const processCategoryFileData = (rawData) => {
  return rawData.map(item => ({
    id: item._id,
    title: item.title,
    tags: Array.isArray(item.tags) ? item.tags.slice(0, 4) : [],
    downloadCount: item.download_count || 0,
    viewCount: item.view_count || 0,
    shareCount: item.share_count || 0,
    fileInfo: {
      type: (item.file_type || 'PDF').toUpperCase(),
      pages: item.pages || 0,
      size: formatCategoryFileSize(item.file_size),
      features: Array.isArray(item.features) ? item.features.slice(0, 2) : [] // 最多显示2个特性
    },
    subject: item.subject || '',
    grade: item.grade || '',
    volume: item.volume || '',
    section: item.section || '',
    sortOrder: item.sort_order || 0,
    createdTime: item.created_time
  }))
}

/**
 * 处理升学专区文件数据
 */
const processCategoryUpgradeData = (rawData) => {
  return rawData.map(item => ({
    id: item._id,
    title: item.title,
    tags: Array.isArray(item.tags) ? item.tags.slice(0, 4) : [],
    downloadCount: item.download_count || 0,
    viewCount: item.view_count || 0,
    shareCount: item.share_count || 0,
    fileInfo: {
      type: (item.file_type || 'PDF').toUpperCase(),
      pages: item.pages || 0,
      size: formatCategoryFileSize(item.file_size),
      features: Array.isArray(item.features) ? item.features.slice(0, 2) : []
    },
    subject: item.subject || '综合',
    grade: item.grade || '', // 幼升小/小升初
    volume: item.volume || '',
    section: item.section || '',
    sortOrder: item.sort_order || 0,
    createdTime: item.created_time
  }))
}

/**
 * 格式化分类页文件大小
 */
const formatCategoryFileSize = (size) => {
  if (!size || size <= 0) return ''
  
  if (size >= 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(1) + 'MB'
  } else if (size >= 1024) {
    return (size / 1024).toFixed(1) + 'KB'
  } else {
    return size + 'B'
  }
}

/**
 * 生成分类页文件信息显示文本
 */
const generateCategoryFileInfo = (file) => {
  const parts = []
  
  // 文件类型
  if (file.fileInfo.type) {
    parts.push(file.fileInfo.type)
  }
  
  // 页数信息
  if (file.fileInfo.pages > 0) {
    parts.push(`${file.fileInfo.pages}页`)
  }
  
  // 文件大小
  if (file.fileInfo.size) {
    parts.push(file.fileInfo.size)
  }
  
  // 特性信息（最多显示2个）
  if (file.fileInfo.features && file.fileInfo.features.length > 0) {
    parts.push(...file.fileInfo.features.slice(0, 2))
  }
  
  return parts.join('·')
}

/**
 * 根据排序权重获取分类页显示标签
 */
const getCategoryDisplayLabel = (sortOrder) => {
  if (sortOrder >= 1000) return '置顶'
  if (sortOrder >= 500) return '热门'
  if (sortOrder >= 100) return '推荐'
  return ''
}

/**
 * 构建分类页筛选条件显示文本
 */
const buildCategoryFilterText = (filters, mode) => {
  const parts = []
  
  if (mode === 'normal') {
    if (filters.selectedGrade) parts.push(filters.selectedGrade)
    if (filters.selectedSubject) parts.push(filters.selectedSubject)
    if (filters.selectedVolume) parts.push(filters.selectedVolume)
    if (filters.selectedSection) parts.push(filters.selectedSection)
  } else {
    if (filters.selectedUpgradeType) parts.push(filters.selectedUpgradeType)
    if (filters.selectedCategory) parts.push(filters.selectedCategory)
  }
  
  return parts.length > 0 ? parts.join(' · ') : '全部'
}

/**
 * 检查分类页是否有筛选条件
 */
const hasCategoryFilters = (filters, mode) => {
  if (mode === 'normal') {
    return !!(filters.selectedGrade || filters.selectedSubject || 
             filters.selectedVolume || filters.selectedSection)
  } else {
    return !!(filters.selectedUpgradeType || filters.selectedCategory)
  }
}

/**
 * 清空分类页筛选条件
 */
const clearCategoryFilters = (mode) => {
  if (mode === 'normal') {
    return {
      selectedGrade: '',
      selectedSubject: '',
      selectedVolume: '',
      selectedSection: ''
    }
  } else {
    return {
      selectedUpgradeType: '',
      selectedCategory: ''
    }
  }
}

module.exports = {
  processCategoryFileData,
  processCategoryUpgradeData,
  formatCategoryFileSize,
  generateCategoryFileInfo,
  getCategoryDisplayLabel,
  buildCategoryFilterText,
  hasCategoryFilters,
  clearCategoryFilters
}