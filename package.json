{"name": "k12-dev-tool", "version": "1.0.0", "description": "K12项目开发工具、文档和测试脚本", "main": "index.js", "scripts": {"test": "node scripts/test/run-all-tests.js", "test:database": "node scripts/test/database-test.js", "test:api": "node scripts/test/api-test.js", "test:performance": "node scripts/test/performance-test.js", "generate:data": "node tools/data-generator/files-generator.js", "generate:feedback": "node tools/data-generator/feedback-generator.js", "validate:config": "node tools/config-validator/cloudbase-validator.js", "validate:env": "node tools/config-validator/env-validator.js", "backup:database": "node scripts/database/backup.js", "restore:database": "node scripts/database/restore.js", "deploy:staging": "node scripts/deploy/staging.js", "deploy:production": "node scripts/deploy/production.js", "docs:build": "node scripts/docs/build-docs.js", "docs:serve": "node scripts/docs/serve-docs.js", "lint": "eslint scripts/ tools/ --ext .js", "clean": "node scripts/utils/clean.js"}, "keywords": ["k12", "education", "development", "tools", "testing", "documentation"], "author": "K12 Development Team", "license": "MIT", "dependencies": {"@cloudbase/node-sdk": "^3.10.1", "axios": "^1.6.0", "chalk": "^4.1.2", "commander": "^11.1.0", "dotenv": "^16.3.1", "fs-extra": "^11.1.1", "inquirer": "^8.2.6", "lodash": "^4.17.21", "moment": "^2.29.4", "ora": "^5.4.1", "progress": "^2.0.3"}, "devDependencies": {"eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/k12-education-platform.git", "directory": "k12-dev-tool"}, "bugs": {"url": "https://github.com/your-username/k12-education-platform/issues"}, "homepage": "https://github.com/your-username/k12-education-platform/tree/main/k12-dev-tool#readme"}