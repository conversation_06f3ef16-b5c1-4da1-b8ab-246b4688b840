// 用户反馈管理API
import axios from 'axios'

const API_BASE = '/api/feedback'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081',
  timeout: 30000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('反馈API请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('响应错误:', error)
    const message = error.response?.data?.error || error.message || '请求失败'
    return Promise.reject(new Error(message))
  }
)

export const feedbackApi = {
  // 获取反馈列表
  getFeedbackList(params = {}) {
    return api.get(API_BASE, { params })
  },

  // 获取反馈详情
  getFeedbackDetail(id) {
    return api.get(`${API_BASE}/${id}`)
  },

  // 更新反馈状态
  updateFeedbackStatus(id, status) {
    return api.put(`${API_BASE}/${id}/status`, { status })
  },

  // 批量更新反馈状态
  batchUpdateStatus(feedbackIds, status) {
    return api.put(`${API_BASE}/batch/status`, {
      feedbackIds,
      status
    })
  },

  // 删除反馈
  deleteFeedback(id) {
    return api.delete(`${API_BASE}/${id}`)
  },

  // 批量删除反馈
  batchDeleteFeedback(feedbackIds) {
    return api.delete(`${API_BASE}/batch`, {
      data: { feedbackIds }
    })
  },

  // 获取反馈统计数据
  getFeedbackStats() {
    return api.get(`${API_BASE}/stats`)
  },

  // 导出反馈数据
  exportFeedback(params = {}) {
    return api.get(`${API_BASE}/export`, {
      params,
      responseType: 'blob'
    })
  }
}
