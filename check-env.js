const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkItem(name, check, fix = null) {
  try {
    const result = check();
    if (result) {
      log(`✅ ${name}`, 'green');
      return true;
    } else {
      log(`❌ ${name}`, 'red');
      if (fix) {
        log(`   💡 建议: ${fix}`, 'yellow');
      }
      return false;
    }
  } catch (error) {
    log(`❌ ${name}: ${error.message}`, 'red');
    if (fix) {
      log(`   💡 建议: ${fix}`, 'yellow');
    }
    return false;
  }
}

function checkEnvironment() {
  log('=== K12 Admin 环境检查工具 ===', 'cyan');
  log('', 'reset');

  let allPassed = true;

  // 检查 Node.js 版本
  allPassed &= checkItem(
    'Node.js 版本 (>= 16.0.0)',
    () => {
      const version = process.version;
      const major = parseInt(version.slice(1).split('.')[0]);
      log(`   当前版本: ${version}`, 'blue');
      return major >= 16;
    },
    '请从 https://nodejs.org/ 下载并安装 Node.js 16.0.0 或更高版本'
  );

  // 检查 npm 版本
  allPassed &= checkItem(
    'npm 版本',
    () => {
      const version = execSync('npm --version', { encoding: 'utf8' }).trim();
      log(`   当前版本: ${version}`, 'blue');
      return true;
    },
    'npm 通常随 Node.js 一起安装'
  );

  // 检查项目文件
  allPassed &= checkItem(
    '项目根目录 package.json',
    () => fs.existsSync(path.join(__dirname, 'package.json')),
    '确保在正确的项目目录中运行此脚本'
  );

  allPassed &= checkItem(
    '后端项目 package.json',
    () => fs.existsSync(path.join(__dirname, 'backend', 'package.json')),
    '检查 backend 目录是否存在'
  );

  allPassed &= checkItem(
    '前端项目 package.json',
    () => fs.existsSync(path.join(__dirname, 'frontend', 'package.json')),
    '检查 frontend 目录是否存在'
  );

  // 检查环境配置文件
  allPassed &= checkItem(
    '环境配置文件 .env',
    () => fs.existsSync(path.join(__dirname, '.env')),
    '创建 .env 文件并配置必要的环境变量'
  );

  // 检查启动脚本
  allPassed &= checkItem(
    '启动脚本 start.js',
    () => fs.existsSync(path.join(__dirname, 'start.js')),
    '确保 start.js 文件存在'
  );

  allPassed &= checkItem(
    'Windows 启动脚本 run.bat',
    () => fs.existsSync(path.join(__dirname, 'run.bat')),
    '确保 run.bat 文件存在（Windows 用户）'
  );

  // 检查依赖安装
  const backendNodeModules = path.join(__dirname, 'backend', 'node_modules');
  const frontendNodeModules = path.join(__dirname, 'frontend', 'node_modules');

  checkItem(
    '后端依赖安装',
    () => fs.existsSync(backendNodeModules),
    '运行 "npm run install:all" 安装依赖'
  );

  checkItem(
    '前端依赖安装',
    () => fs.existsSync(frontendNodeModules),
    '运行 "npm run install:all" 安装依赖'
  );

  // 检查端口占用
  try {
    const { exec } = require('child_process');
    exec('netstat -ano | findstr :8080', (error, stdout) => {
      if (stdout) {
        log('⚠️  端口 8080 被占用', 'yellow');
        log('   💡 启动脚本会自动处理端口占用问题', 'blue');
      } else {
        log('✅ 端口 8080 可用', 'green');
      }
    });

    exec('netstat -ano | findstr :8081', (error, stdout) => {
      if (stdout) {
        log('⚠️  端口 8081 被占用', 'yellow');
        log('   💡 启动脚本会自动处理端口占用问题', 'blue');
      } else {
        log('✅ 端口 8081 可用', 'green');
      }
    });
  } catch (error) {
    log('ℹ️  无法检查端口占用（非 Windows 系统）', 'blue');
  }

  log('', 'reset');

  if (allPassed) {
    log('🎉 环境检查通过！可以开始使用 K12 Admin 系统', 'green');
    log('', 'reset');
    log('启动方式：', 'cyan');
    log('  方式一：双击 run.bat（Windows）', 'blue');
    log('  方式二：运行 npm run dev', 'blue');
    log('', 'reset');
    log('访问地址：http://localhost:8080', 'cyan');
  } else {
    log('❌ 环境检查未通过，请根据上述建议修复问题', 'red');
    log('', 'reset');
    log('常见解决方案：', 'cyan');
    log('  1. 安装 Node.js 16.0.0+', 'blue');
    log('  2. 运行 npm run install:all 安装依赖', 'blue');
    log('  3. 检查项目文件完整性', 'blue');
  }

  log('', 'reset');
  log('如需帮助，请查看 README.md 或联系开发团队', 'yellow');
}

// 运行环境检查
checkEnvironment();
