# K12教育资源小程序 - 广告功能实现计划（简化版）

## 📋 概述

本文档制定K12教育资源小程序广告功能的实现计划，专注于广告展示和激励视频下载功能，保持简单实用的设计原则。

## 🎯 功能目标

### 核心功能
1. **激励视频广告**：用户观看完整视频后可下载资料
2. **页面展示广告**：首页底部、详情页中间、搜索页顶部等位置的Banner/原生广告
3. **广告配置管理**：通过独立的ads集合管理所有广告位配置

### 设计原则
- **简单实用**：只实现必要的广告功能，避免过度设计
- **用户体验优先**：广告不影响正常使用流程
- **配置灵活**：支持动态开启/关闭广告位
- **维护简单**：最小化代码复杂度

## 🎯 广告平台选择

### 微信小程序原生广告（推荐）

#### 支持的广告类型
- **Banner广告**：横幅广告，适合页面顶部/底部
- **激励视频广告**：用户观看完整视频获得下载权限
- **原生模板广告**：与内容融合度高的信息流广告

#### 技术特点
```javascript
// 1. 激励视频广告
const rewardedVideoAd = wx.createRewardedVideoAd({
  adUnitId: 'adunit-xxx'
})

// 2. Banner广告（组件形式）
<ad unit-id="adunit-xxx" ad-type="banner"></ad>

// 3. 原生模板广告
<ad unit-id="adunit-xxx" ad-type="template"></ad>
```

#### 选择理由
- ✅ **官方支持**：稳定性高，审核通过率高
- ✅ **集成简单**：无需额外SDK，开发成本低
- ✅ **维护成本低**：无需处理第三方SDK兼容性
- ✅ **收益可靠**：腾讯官方结算，资金安全

## 🗄️ 数据库设计方案

### 新增ads集合（广告配置）

#### 集合结构设计
```javascript
// ads集合 - 广告位配置
{
  _id: ObjectId,
  position: String,        // 广告位标识：home_bottom/detail_middle/download_button/search_top/list_insert
  name: String,           // 广告位名称：首页底部广告/详情页中间广告/下载按钮广告等
  description: String,    // 广告位描述
  ad_type: String,       // 广告类型：banner/reward_video/native
  config: {              // 广告配置
    ad_id: String,      // 微信广告位ID
    provider: String    // 广告商：tencent（固定）
  },
  status: String,       // 状态：active/inactive/testing/maintenance
  created_time: Date,
  updated_time: Date
}
```

#### 状态字段说明
- **active** - 正常启用，展示广告
- **inactive** - 禁用，不展示广告
- **testing** - 测试状态，可用于A/B测试
- **maintenance** - 维护状态，临时禁用

#### 广告位定义（基于页面结构分析）

| position | name | ad_type | 插入位置 | 文件路径 | 说明 |
|----------|------|---------|----------|----------|------|
| splash_screen | 开屏广告 | interstitial | 小程序启动时 | `app.js` | 启动时展示，默认关闭 |
| home_bottom | 首页底部广告 | banner | 推荐资料section之后 | `pages/index/index.wxml` | 第96行后插入，页面底部 |
| detail_middle | 详情页中间广告 | native | 资料介绍与预览之间 | `pages/files-detail/files-detail.wxml` | 第59-61行之间插入 |
| download_button | 下载按钮广告 | reward_video | 下载按钮点击触发 | `pages/files-detail/files-detail.js` | 已有实现，需优化配置 |
| search_top | 搜索页顶部广告 | banner | 搜索结果上方 | `pages/search/search.wxml` | 第73-76行已实现 |
| list_insert | 列表页插入广告 | native | 每5个资料项后插入 | `pages/files-list/files-list.wxml` | 第62行循环中插入 |

#### 详细插入位置分析

##### 1. 开屏广告 (`splash_screen`)
**文件**: `app.js`
**插入位置**: `onLaunch`生命周期中
**展示策略**: 默认关闭，通过配置控制开启
```javascript
// app.js
App({
  onLaunch() {
    // 初始化开屏广告
    this.initSplashAd()
  },

  async initSplashAd() {
    // 获取开屏广告配置
    const adConfig = await getSplashAdConfig()
    if (adConfig.status === 'active') {
      this.showSplashAd(adConfig)
    }
  }
})
```

##### 2. 首页底部广告 (`home_bottom`)
**文件**: `pages/index/index.wxml`
**插入位置**: 第96行推荐资料section之后
```xml
<!-- 推荐资料 -->
<view class="recommend-section">
  <!-- 现有推荐资料内容 -->
</view>

<!-- 🆕 首页底部广告位 -->
<home-banner-ad></home-banner-ad>

<!-- 加载状态 -->
<view class="loading" wx:if="{{loading}}">
```

##### 2. 详情页中间广告 (`detail_middle`)
**文件**: `pages/files-detail/files-detail.wxml`
**插入位置**: 第59-61行资料介绍与预览之间
```xml
<!-- 资料描述 -->
<view class="section-card">
  <view class="section-title">资料介绍</view>
  <view class="description-content">{{materialInfo.description}}</view>
</view>

<!-- 🆕 详情页中间广告位 -->
<detail-native-ad></detail-native-ad>

<!-- 资料预览 -->
<view class="section-card">
  <view class="section-title">📖 资料预览</view>
```

##### 3. 激励视频广告 (`download_button`)
**文件**: `pages/files-detail/files-detail.js`
**现状**: 已有实现，需要优化配置获取方式
**位置**: 下载按钮点击事件中触发

##### 4. 搜索页顶部广告 (`search_top`)
**文件**: `pages/search/search.wxml`
**现状**: 第73-76行已实现，完成度90%
```xml
<!-- 搜索页顶部广告 -->
<view class="search-ad-top" wx:if="{{searchResults.length > 0 && adConfig.enabled}}">
  <ad unit-id="{{adConfig.ad_id}}" ad-type="{{adConfig.ad_type}}" ad-intervals="30"></ad>
</view>
```

##### 6. 列表页插入广告 (`list_insert`)
**文件**: `pages/files-list/files-list.wxml`
**插入位置**: 第62行资料列表循环中，每5个资料项后插入
```xml
<view class="material-item" wx:for="{{materialList}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
  <!-- 现有资料项内容 -->
</view>

<!-- 🆕 列表页插入广告位（每5个资料后显示） -->
<list-native-ad wx:if="{{(index + 1) % 5 === 0}}"></list-native-ad>
```

#### 初始化数据示例
```javascript
// 开屏广告配置（默认关闭）
{
  position: "splash_screen",
  name: "开屏广告",
  description: "小程序启动时展示的全屏插屏广告",
  ad_type: "interstitial",
  config: {
    ad_id: "adunit-splash-001",
    provider: "tencent",
    skip_time: 3,              // 3秒后可跳过
    max_daily_shows: 3,        // 每日最多显示3次
    show_frequency: "session"  // 每次会话显示：session/daily_first/always
  },
  status: "inactive",  // 默认关闭，需要时开启
  created_time: new Date(),
  updated_time: new Date()
}

// 下载按钮激励视频广告配置
{
  position: "download_button",
  name: "下载按钮激励视频广告",
  description: "用户点击下载时播放的激励视频广告",
  ad_type: "reward_video",
  config: {
    ad_id: "adunit-87654321",
    provider: "tencent"
  },
  status: "active",
  created_time: new Date(),
  updated_time: new Date()
}

// 首页底部横幅广告配置
{
  position: "home_bottom",
  name: "首页底部横幅广告",
  description: "首页底部展示的横幅广告",
  ad_type: "banner",
  config: {
    ad_id: "adunit-12345678",
    provider: "tencent"
  },
  status: "active",
  created_time: new Date(),
  updated_time: new Date()
}

// 测试状态的广告配置示例
{
  position: "detail_middle",
  name: "详情页中间原生广告",
  description: "详情页资料介绍下方的原生广告",
  ad_type: "native",
  config: {
    ad_id: "adunit-11111111",
    provider: "tencent"
  },
  status: "testing",  // 测试状态，可用于A/B测试
  created_time: new Date(),
  updated_time: new Date()
}
```

### 保持files集合不变

**说明**：files集合已有`ad_required_count`字段用于控制激励视频观看次数，无需修改。

#### files集合字段说明
```javascript
// files集合中的广告相关字段
{
  _id: "file_001",
  title: "小学数学一年级上册期末试卷",
  ad_required_count: 2,  // 需要观看2次激励视频才能下载
  // ... 其他字段
}
```

### 简化实现：页面内计数

**说明**：不新增数据库集合，在页面内实现多次观看计数，页面刷新后重新开始。

#### 实现原理
1. **页面data中记录观看进度**：`watchedCount`、`requiredCount`
2. **每次观看完整广告**：`watchedCount + 1`
3. **达到要求次数**：允许下载
4. **页面刷新重置**：重新开始计数（合理的产品逻辑）

### 数据库权限配置

```javascript
// ads集合权限规则
{
  "read": true,    // 小程序可读取广告配置
  "write": false   // 小程序不可写入，只有管理员可以修改
}
```



## 🛠️ 技术实现方案

### 1. 广告配置API（按页面解耦）

#### 详情页广告配置API
```javascript
// utils/api/detailAdApi.js
/**
 * 详情页专用广告API接口
 * 严格按照项目解耦规则：仅供详情页使用，其他页面禁止调用
 */

const db = wx.cloud.database()

/**
 * 获取下载按钮激励视频广告配置
 * @returns {Promise<Object>} 广告配置对象
 */
const getDetailDownloadAdConfig = async () => {
  try {
    const result = await db.collection('ads')
      .where({
        position: 'download_button',
        status: 'active'
      })
      .limit(1)
      .get()

    if (result.data.length > 0) {
      const config = result.data[0]
      return {
        success: true,
        data: config
      }
    } else {
      return {
        success: true,
        data: {
          status: 'inactive'  // 使用status字段替代config.enabled
        }
      }
    }
  } catch (error) {
    console.error('获取详情页下载广告配置失败:', error)
    return {
      success: true,
      data: {
        status: 'inactive'  // 使用status字段替代config.enabled
      }
    }
  }
}

/**
 * 获取详情页中间原生广告配置
 * @returns {Promise<Object>} 广告配置对象
 */
const getDetailMiddleAdConfig = async () => {
  try {
    const result = await db.collection('ads')
      .where({
        position: 'detail_middle',
        status: 'active'
      })
      .limit(1)
      .get()

    if (result.data.length > 0) {
      const config = result.data[0]
      return {
        success: true,
        data: config
      }
    } else {
      return {
        success: true,
        data: {
          config: { enabled: false }
        }
      }
    }
  } catch (error) {
    console.error('获取详情页中间广告配置失败:', error)
    return {
      success: true,
      data: {
        config: { enabled: false }
      }
    }
  }
}

module.exports = {
  getDetailDownloadAdConfig,
  getDetailMiddleAdConfig
}
```

#### 首页广告配置API
```javascript
// utils/api/homeAdApi.js
/**
 * 首页专用广告API接口
 * 严格按照项目解耦规则：仅供首页使用，其他页面禁止调用
 */

const db = wx.cloud.database()

/**
 * 获取首页底部横幅广告配置
 * @returns {Promise<Object>} 广告配置对象
 */
const getHomeBottomAdConfig = async () => {
  try {
    const result = await db.collection('ads')
      .where({
        position: 'home_bottom',
        status: 'active'
      })
      .limit(1)
      .get()

    if (result.data.length > 0) {
      const config = result.data[0]
      return {
        success: true,
        data: config
      }
    } else {
      return {
        success: true,
        data: {
          config: { enabled: false }
        }
      }
    }
  } catch (error) {
    console.error('获取首页底部广告配置失败:', error)
    return {
      success: true,
      data: {
        config: { enabled: false }
      }
    }
  }
}

module.exports = {
  getHomeBottomAdConfig
}
```

#### 开屏广告配置API
```javascript
// utils/api/splashAdApi.js
/**
 * 开屏广告专用API接口
 * 全局使用，在app.js中调用
 */

const db = wx.cloud.database()

/**
 * 获取开屏广告配置
 * @returns {Promise<Object>} 广告配置对象
 */
const getSplashAdConfig = async () => {
  try {
    const result = await db.collection('ads')
      .where({
        position: 'splash_screen',
        status: 'active'
      })
      .limit(1)
      .get()

    if (result.data.length > 0) {
      const config = result.data[0]
      return {
        success: true,
        data: config
      }
    } else {
      return {
        success: true,
        data: {
          status: 'inactive'
        }
      }
    }
  } catch (error) {
    console.error('获取开屏广告配置失败:', error)
    return {
      success: true,
      data: {
        status: 'inactive'
      }
    }
  }
}

/**
 * 检查开屏广告展示频次
 * @param {Object} config - 广告配置
 * @returns {Boolean} 是否可以展示
 */
const canShowSplashAd = (config) => {
  if (!config || config.status !== 'active') {
    return false
  }

  const today = new Date().toDateString()
  const storageKey = 'splash_ad_history'

  try {
    const history = wx.getStorageSync(storageKey) || []
    const todayShows = history.filter(time =>
      new Date(time).toDateString() === today
    ).length

    // 检查每日最大展示次数
    if (config.config.max_daily_shows && todayShows >= config.config.max_daily_shows) {
      return false
    }

    // 检查展示频次
    if (config.config.show_frequency === 'daily_first' && todayShows > 0) {
      return false
    }

    return true
  } catch (error) {
    console.error('检查开屏广告展示频次失败:', error)
    return false
  }
}

/**
 * 记录开屏广告展示
 */
const recordSplashAdShow = () => {
  try {
    const storageKey = 'splash_ad_history'
    const history = wx.getStorageSync(storageKey) || []

    history.push(Date.now())

    // 只保留最近30天的记录
    const thirtyDaysAgo = Date.now() - 30 * 24 * 60 * 60 * 1000
    const recentHistory = history.filter(time => time > thirtyDaysAgo)

    wx.setStorageSync(storageKey, recentHistory)
  } catch (error) {
    console.error('记录开屏广告展示失败:', error)
  }
}

module.exports = {
  getSplashAdConfig,
  canShowSplashAd,
  recordSplashAdShow
}
```

### 2. 开屏广告管理器

#### 全局开屏广告管理器
```javascript
// utils/splashAdManager.js
/**
 * 开屏广告管理器
 * 在app.js中使用，全局管理开屏广告
 */

const { getSplashAdConfig, canShowSplashAd, recordSplashAdShow } = require('./api/splashAdApi')

class SplashAdManager {
  constructor() {
    this.adInstance = null
    this.adConfig = null
    this.isShowing = false
  }

  // 初始化开屏广告
  async init() {
    try {
      const result = await getSplashAdConfig()
      if (!result.success || !canShowSplashAd(result.data)) {
        console.log('开屏广告未启用或不满足展示条件')
        return false
      }

      this.adConfig = result.data

      if (!wx.createInterstitialAd) {
        console.warn('当前环境不支持插屏广告')
        return false
      }

      this.adInstance = wx.createInterstitialAd({
        adUnitId: this.adConfig.config.ad_id
      })

      // 绑定事件
      this.adInstance.onLoad(() => {
        console.log('开屏广告加载成功')
      })

      this.adInstance.onError((err) => {
        console.error('开屏广告加载失败:', err)
        this.isShowing = false
      })

      this.adInstance.onClose(() => {
        console.log('开屏广告关闭')
        this.isShowing = false
        recordSplashAdShow()
      })

      return true
    } catch (error) {
      console.error('初始化开屏广告失败:', error)
      return false
    }
  }

  // 显示开屏广告
  async show() {
    if (!this.adInstance || this.isShowing) {
      return false
    }

    try {
      this.isShowing = true
      await this.adInstance.show()
      return true
    } catch (error) {
      console.error('显示开屏广告失败:', error)
      this.isShowing = false
      return false
    }
  }

  // 销毁广告实例
  destroy() {
    if (this.adInstance) {
      this.adInstance.destroy()
      this.adInstance = null
      this.isShowing = false
    }
  }
}

// 导出单例
module.exports = new SplashAdManager()
```

### 3. 详情页激励视频广告工具

#### 详情页专用激励视频广告工具
```javascript
// utils/helpers/detailAdHelpers.js
/**
 * 详情页专用广告工具函数
 * 严格按照项目解耦规则：仅供详情页使用，其他页面禁止调用
 */

const { getDetailDownloadAdConfig } = require('../api/detailAdApi')

/**
 * 初始化详情页激励视频广告
 * @returns {Promise<Object>} 返回广告实例和配置
 */
const initDetailRewardVideoAd = async () => {
  try {
    // 获取广告配置
    const configResult = await getDetailDownloadAdConfig()
    if (!configResult.success || !configResult.data.config.enabled) {
      console.log('详情页激励视频广告未启用')
      return {
        success: false,
        adInstance: null,
        config: null
      }
    }

    const adConfig = configResult.data

    // 检查环境支持
    if (!wx.createRewardedVideoAd) {
      console.warn('当前环境不支持激励视频广告')
      return {
        success: false,
        adInstance: null,
        config: adConfig
      }
    }

    // 创建广告实例
    const adInstance = wx.createRewardedVideoAd({
      adUnitId: adConfig.config.ad_id
    })

    return {
      success: true,
      adInstance: adInstance,
      config: adConfig
    }
  } catch (error) {
    console.error('初始化详情页激励视频广告失败:', error)
    return {
      success: false,
      adInstance: null,
      config: null
    }
  }
}

/**
 * 显示激励视频广告并处理结果
 * @param {Object} adInstance - 广告实例
 * @returns {Promise<Object>} 广告观看结果
 */
const showDetailRewardVideoAd = (adInstance) => {
  return new Promise((resolve, reject) => {
    if (!adInstance) {
      reject(new Error('广告实例不存在'))
      return
    }

    // 监听广告关闭事件
    adInstance.onClose((res) => {
      resolve(res)
    })

    // 监听广告错误事件
    adInstance.onError((err) => {
      reject(err)
    })

    // 显示广告
    adInstance.show().catch(reject)
  })
}

module.exports = {
  initDetailRewardVideoAd,
  showDetailRewardVideoAd
}
```

### 3. 页面集成实现

#### app.js开屏广告集成
```javascript
// app.js
const splashAdManager = require('./utils/splashAdManager')

App({
  globalData: {
    userInfo: null,
    splashAdShown: false  // 标记本次会话是否已显示开屏广告
  },

  onLaunch() {
    console.log('小程序启动')

    // 初始化云开发
    this.initCloud()

    // 初始化开屏广告（延迟执行，避免影响启动速度）
    setTimeout(() => {
      this.initSplashAd()
    }, 500)
  },

  onShow() {
    console.log('小程序显示')

    // 如果是从后台切换回来，且本次会话未显示过开屏广告，则尝试显示
    if (!this.globalData.splashAdShown) {
      setTimeout(() => {
        this.initSplashAd()
      }, 300)
    }
  },

  // 初始化开屏广告
  async initSplashAd() {
    try {
      const success = await splashAdManager.init()
      if (success) {
        const shown = await splashAdManager.show()
        if (shown) {
          this.globalData.splashAdShown = true
        }
      }
    } catch (error) {
      console.error('开屏广告初始化失败:', error)
    }
  },

  // 初始化云开发
  initCloud() {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        traceUser: true
      })
    }
  },

  onHide() {
    console.log('小程序隐藏')
  },

  onError(error) {
    console.error('小程序错误:', error)
  }
})
```

#### 详情页激励视频广告集成（支持多次观看）
```javascript
// pages/files-detail/files-detail.js
const { cloudApi } = require('../../utils/cloudApi')
const { initDetailRewardVideoAd, showDetailRewardVideoAd } = require('../../utils/helpers/detailAdHelpers')

Page({
  data: {
    // 文件详情
    materialInfo: null,
    loading: true,

    // 相关推荐
    relatedMaterials: [],

    // 预览图片
    previewImages: [],

    // 下载状态
    downloading: false,
    downloadProgress: 0,

    // 广告状态
    adLoading: false,
    rewardVideoAd: null,
    adConfig: null,

    // 多次观看状态（页面内计数）
    watchedCount: 0,         // 已观看次数
    requiredCount: 0,        // 需要观看次数
    canDownload: false,      // 是否可以下载
    downloadButtonText: '观看广告下载' // 下载按钮文字
  },

  onLoad(options) {
    const fileId = options.id
    if (fileId) {
      this.loadFileDetail(fileId)
      this.initRewardVideoAd()
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 加载文件详情（需要修改以初始化观看状态）
  async loadFileDetail(fileId) {
    this.setData({ loading: true })

    try {
      const result = await cloudApi.getFileDetail(fileId)
      if (result.success) {
        const materialInfo = result.data
        const requiredCount = materialInfo.ad_required_count || 0

        this.setData({
          materialInfo: materialInfo,
          requiredCount: requiredCount,
          canDownload: requiredCount === 0, // 如果不需要观看广告，直接可下载
          downloadButtonText: this.getDownloadButtonText(0, requiredCount)
        })

        // 加载相关推荐
        this.loadRelatedMaterials(materialInfo)
      } else {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载文件详情失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 获取下载按钮文字
  getDownloadButtonText(watchedCount, requiredCount) {
    if (requiredCount === 0) {
      return '立即下载'
    } else if (watchedCount >= requiredCount) {
      return '立即下载'
    } else {
      return `观看广告下载 (${watchedCount}/${requiredCount})`
    }
  },

  onUnload() {
    // 清理广告资源
    if (this.data.rewardVideoAd) {
      this.data.rewardVideoAd.destroy()
    }
  },

  // 初始化激励视频广告
  async initRewardVideoAd() {
    try {
      const result = await initDetailRewardVideoAd()

      if (result.success && result.adInstance) {
        // 绑定广告事件
        result.adInstance.onLoad(() => {
          console.log('激励视频广告加载成功')
        })

        result.adInstance.onError((err) => {
          console.error('激励视频广告加载失败:', err)
        })

        this.setData({
          rewardVideoAd: result.adInstance,
          adConfig: result.config
        })
      } else {
        console.log('激励视频广告初始化失败或未启用')
        this.setData({
          rewardVideoAd: null,
          adConfig: null
        })
      }
    } catch (error) {
      console.error('初始化激励视频广告失败:', error)
      this.setData({
        rewardVideoAd: null,
        adConfig: null
      })
    }
  },

  // 下载资源（点击下载按钮）
  async downloadResource() {
    const materialInfo = this.data.materialInfo
    if (!materialInfo || this.data.downloading) return

    // 检查是否可以直接下载
    if (this.data.canDownload) {
      this.startDownload()
      return
    }

    // 需要观看激励视频广告
    const requiredCount = this.data.requiredCount
    const watchedCount = this.data.watchedCount

    if (requiredCount > 0 && this.data.rewardVideoAd) {
      this.setData({ adLoading: true })

      try {
        const result = await showDetailRewardVideoAd(this.data.rewardVideoAd)

        if (result && result.isEnded) {
          // 用户观看完整视频，更新页面计数
          this.updateWatchCount(true)
        } else {
          // 用户中途关闭广告
          wx.showToast({
            title: '请观看完整广告后下载',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('显示激励视频广告失败:', error)
        // 广告失败时的降级处理
        this.showDownloadFallback()
      } finally {
        this.setData({ adLoading: false })
      }
    } else {
      // 广告未准备好，降级处理
      this.showDownloadFallback()
    }
  },

  // 更新观看计数（页面内计数）
  updateWatchCount(completed) {
    if (!completed) return

    const watchedCount = this.data.watchedCount + 1
    const requiredCount = this.data.requiredCount
    const canDownload = watchedCount >= requiredCount

    this.setData({
      watchedCount: watchedCount,
      canDownload: canDownload,
      downloadButtonText: this.getDownloadButtonText(watchedCount, requiredCount)
    })

    // 如果已完成所有观看，开始下载
    if (canDownload) {
      wx.showToast({
        title: '广告观看完成！',
        icon: 'success'
      })
      setTimeout(() => {
        this.startDownload()
      }, 1000)
    } else {
      // 显示进度提示
      const remaining = requiredCount - watchedCount
      wx.showToast({
        title: `还需观看${remaining}次广告`,
        icon: 'none'
      })
    }
  },

  // 降级处理：广告失败时的备选方案
  showDownloadFallback() {
    wx.showModal({
      title: '提示',
      content: '广告加载失败，是否直接下载？',
      success: (res) => {
        if (res.confirm) {
          this.startDownload()
        }
      }
    })
  },

  // 开始下载文件
  startDownload() {
    // 原有的下载逻辑保持不变
    console.log('开始下载文件')
    this.setData({ downloading: true })
    // ... 原有下载实现
  }

  // 开始下载文件
  startDownload() {
    // 原有的下载逻辑保持不变
    console.log('开始下载文件')
    this.setData({ downloading: true })
    // ... 原有下载实现
  },

  // 降级处理：广告失败时的备选方案
  showDownloadFallback() {
    wx.showModal({
      title: '提示',
      content: '广告加载失败，是否直接下载？',
      success: (res) => {
        if (res.confirm) {
          this.startDownload()
        }
      }
    })
  }

  // ... 其他原有方法保持不变
})
```

#### 详情页WXML模板更新（支持多次观看提示）
```xml
<!-- pages/files-detail/files-detail.wxml -->
<!-- 在下载按钮区域添加观看进度提示 -->

<!-- 下载按钮区域 -->
<view class="download-section">
  <!-- 观看进度提示 -->
  <view class="ad-progress" wx:if="{{requiredCount > 0}}">
    <view class="progress-text">
      <text wx:if="{{!canDownload}}">需要观看{{requiredCount}}次广告后下载</text>
      <text wx:else>广告观看完成，可以下载了！</text>
    </view>
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{(watchedCount / requiredCount) * 100}}%"></view>
    </view>
    <view class="progress-count">{{watchedCount}}/{{requiredCount}}</view>
  </view>

  <!-- 下载按钮 -->
  <button
    class="download-btn {{canDownload ? 'can-download' : 'need-ad'}}"
    bindtap="downloadResource"
    disabled="{{downloading || adLoading}}"
  >
    <text wx:if="{{adLoading}}">广告加载中...</text>
    <text wx:elif="{{downloading}}">下载中...</text>
    <text wx:else>{{downloadButtonText}}</text>
  </button>
</view>
```

#### 对应的WXSS样式
```css
/* pages/files-detail/files-detail.wxss */
/* 观看进度样式 */
.ad-progress {
  margin: 20rpx 0;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.progress-text {
  font-size: 28rpx;
  color: #495057;
  text-align: center;
  margin-bottom: 16rpx;
}

.progress-bar {
  height: 8rpx;
  background: #e9ecef;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-count {
  font-size: 24rpx;
  color: #6c757d;
  text-align: center;
}

/* 下载按钮样式 */
.download-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  margin-top: 20rpx;
}

.download-btn.need-ad {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: white;
}

.download-btn.can-download {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  color: white;
}

.download-btn:disabled {
  opacity: 0.6;
}
```

#### 首页Banner广告组件
```javascript
// components/home-banner-ad/home-banner-ad.js
/**
 * 首页专用Banner广告组件
 * 严格按照项目解耦规则：仅供首页使用，其他页面禁止调用
 */

const { getHomeBottomAdConfig } = require('../../utils/api/homeAdApi')

Component({
  data: {
    adConfig: null,
    showAd: false
  },

  lifetimes: {
    attached() {
      this.loadHomeBottomAdConfig()
    }
  },

  methods: {
    // 加载首页底部广告配置
    async loadHomeBottomAdConfig() {
      try {
        const result = await getHomeBottomAdConfig()

        if (result.success && result.data.config.enabled) {
          this.setData({
            adConfig: result.data,
            showAd: true
          })
        }
      } catch (error) {
        console.error('加载首页底部广告配置失败:', error)
      }
    },

    // 广告加载成功
    onAdLoad() {
      console.log('首页底部Banner广告加载成功')
    },

    // 广告加载失败
    onAdError(e) {
      console.error('首页底部Banner广告加载失败:', e.detail)
      this.setData({ showAd: false })
    }
  }
})
```

#### 首页Banner广告组件模板
```xml
<!-- components/home-banner-ad/home-banner-ad.wxml -->
<view class="home-banner-ad-container" wx:if="{{showAd && adConfig}}">
  <ad
    unit-id="{{adConfig.config.ad_id}}"
    ad-type="{{adConfig.ad_type}}"
    bindload="onAdLoad"
    binderror="onAdError"
  ></ad>
</view>
```

#### 首页Banner广告组件样式
```css
/* components/home-banner-ad/home-banner-ad.wxss */
.home-banner-ad-container {
  margin: 20rpx 32rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
```

#### 详情页原生广告组件
```javascript
// components/detail-native-ad/detail-native-ad.js
/**
 * 详情页专用原生广告组件
 * 严格按照项目解耦规则：仅供详情页使用，其他页面禁止调用
 */

const { getDetailMiddleAdConfig } = require('../../utils/api/detailAdApi')

Component({
  data: {
    adConfig: null,
    showAd: false
  },

  lifetimes: {
    attached() {
      this.loadDetailMiddleAdConfig()
    }
  },

  methods: {
    // 加载详情页中间广告配置
    async loadDetailMiddleAdConfig() {
      try {
        const result = await getDetailMiddleAdConfig()

        if (result.success && result.data.config.enabled) {
          this.setData({
            adConfig: result.data,
            showAd: true
          })
        }
      } catch (error) {
        console.error('加载详情页中间广告配置失败:', error)
      }
    },

    // 广告加载成功
    onAdLoad() {
      console.log('详情页中间原生广告加载成功')
    },

    // 广告加载失败
    onAdError(e) {
      console.error('详情页中间原生广告加载失败:', e.detail)
      this.setData({ showAd: false })
    }
  }
})
```

#### 详情页原生广告组件模板
```xml
<!-- components/detail-native-ad/detail-native-ad.wxml -->
<view class="detail-native-ad-container" wx:if="{{showAd && adConfig}}">
  <ad
    unit-id="{{adConfig.config.ad_id}}"
    ad-type="{{adConfig.ad_type}}"
    bindload="onAdLoad"
    binderror="onAdError"
  ></ad>
</view>
```

## 📅 开发计划（基于现状分析）

### 当前实现状态总结
根据08广告功能.md分析，当前实现情况：
- ✅ **激励视频广告**：70%完成（基本功能可用，配置需完善）
- ✅ **搜索页顶部广告**：90%完成（实现较完整）
- ❌ **首页底部广告**：0%完成（完全未开始）
- ❌ **详情页中间广告**：0%完成（完全未开始）
- ❌ **列表页插入广告**：0%完成（完全未开始）

**总体完成度：约30%**

### 第1周：数据库和API开发
- [ ] 创建ads集合并初始化基础数据（包含开屏广告配置）
- [ ] 开发splashAdApi.js（开屏广告配置API）
- [ ] 开发detailAdApi.js（详情页广告配置API）
- [ ] 开发homeAdApi.js（首页广告配置API）
- [ ] 开发detailAdHelpers.js（详情页广告工具函数）
- [ ] 测试广告配置读取功能

### 第2周：核心广告功能完善
**重点**：激励视频广告优化 + 开屏广告实现
- [ ] **开屏广告实现**：开发splashAdManager.js管理器
- [ ] **app.js集成**：在app.js中集成开屏广告（默认关闭状态）
- [ ] 修改详情页files-detail.js集成新的广告API
- [ ] **解决硬编码问题**：从ads集合动态获取广告配置（当前使用硬编码'adunit-87654321'）
- [ ] **修复字段名不一致**：统一使用`ad_required_count`字段（当前代码中使用`adRequiredCount`）
- [ ] 完善广告加载失败的降级处理机制
- [ ] 测试激励视频广告和开屏广告完整流程

### 第3周：页面展示广告实现
**现状**：搜索页广告已实现90%，首页和详情页广告完全未实现
- [ ] 开发home-banner-ad组件（首页专用）
- [ ] 开发detail-native-ad组件（详情页专用）
- [ ] 在首页index.wxml中集成Banner广告组件
- [ ] 在详情页files-detail.wxml中集成原生广告组件
- [ ] **保持搜索页现有实现**：已有getSearchAdConfig，无需修改

### 第4周：测试与优化
- [ ] 各广告位展示测试
- [ ] 多次观看机制测试
- [ ] 开屏广告开关测试
- [ ] 广告加载失败降级测试
- [ ] 激励视频配置动态化测试

### 暂不实现的功能（低优先级）
根据简化原则，以下功能暂不实现：
- ❌ **列表页插入广告**：复杂度高，收益相对较低
- ❌ **广告统计功能**：依赖微信广告平台自带统计
- ❌ **A/B测试功能**：过度设计，当前不需要

## 🎯 实现目标（基于现状优化）

### 核心功能
1. **开屏广告实现**：完整实现开屏广告功能，默认关闭状态（从0%到100%）
2. **激励视频广告优化**：解决硬编码问题，实现动态配置（从70%提升到95%）
3. **页面展示广告补全**：实现首页、详情页广告展示（从0%提升到90%）
4. **搜索页广告保持**：维持现有90%完成度，无需大幅修改
5. **配置管理统一**：通过ads集合替代system_configs中的广告配置

### 重点解决的问题
基于08广告功能.md分析，重点解决以下问题：

#### 1. 激励视频广告配置硬编码问题
**现状问题**：
- 代码中使用硬编码的广告ID：`'adunit-87654321'`
- 注释显示应该"从system_configs表获取"，但未实现

**解决方案**：
- 实现从ads集合动态获取广告配置
- 移除硬编码，支持后台动态调整

#### 2. 字段名不一致问题
**现状问题**：
- PRD设计了`ad_required_count`字段
- 代码中使用了错误的字段名`adRequiredCount`

**解决方案**：
- 统一使用`ad_required_count`字段名
- 确保数据库设计与代码实现一致

#### 3. 广告位缺失问题
**现状问题**：
- 首页底部广告：完全未实现
- 详情页中间广告：完全未实现

**解决方案**：
- 开发专用广告组件
- 在对应页面集成广告展示

### 技术特点
- **基于现状优化**：在现有30%基础上提升到85%完成度
- **问题导向**：重点解决已识别的具体问题
- **配置灵活**：支持后台动态调整广告配置
- **降级处理**：广告加载失败时的用户友好处理
- **维护简单**：代码结构清晰，易于维护

## 🔧 技术要点（基于现状分析）

### 关键实现
1. **严格解耦**：按照项目解耦规则，每个页面使用独立的广告API
2. **配置动态化**：解决硬编码问题，从ads集合获取配置
3. **字段名统一**：修复`ad_required_count`与`adRequiredCount`不一致问题
4. **错误处理**：广告加载失败时的降级机制
5. **用户体验**：确保广告不影响正常使用流程

### 错误处理机制

#### 广告加载失败处理
- 激励视频广告失败：弹窗询问用户是否直接下载
- 其他广告位失败：静默处理，不影响正常功能
- 网络异常：提示用户检查网络连接

#### 2. 网络异常处理
```javascript
// utils/helpers/networkHandler.js
/**
 * 网络异常处理
 */

const checkNetworkStatus = async () => {
  return new Promise((resolve) => {
    wx.getNetworkType({
      success: (res) => {
        resolve({
          isConnected: res.networkType !== 'none',
          networkType: res.networkType
        })
      },
      fail: () => {
        resolve({
          isConnected: false,
          networkType: 'unknown'
        })
      }
    })
  })
}

const handleNetworkError = async (retryCallback) => {
  const network = await checkNetworkStatus()

  if (!network.isConnected) {
    wx.showModal({
      title: '网络异常',
      content: '请检查网络连接后重试',
      showCancel: false,
      success: () => {
        // 可以提供重试选项
        if (retryCallback) {
          setTimeout(retryCallback, 1000)
        }
      }
    })
    return false
  }

  return true
}

module.exports = {
  checkNetworkStatus,
  handleNetworkError
}
```

### 现有代码兼容性
基于08广告功能.md分析，需要兼容现有实现：

1. **保持搜索页广告**：
   - 现有`utils/api/systemConfigApi.js`中的`getAdConfig`函数
   - 搜索页的广告实现已经90%完成，保持不变

2. **激励视频广告升级**：
   - 现有`pages/files-detail/files-detail.js`中的`initRewardVideoAd`方法
   - 需要修改硬编码部分，保持其他逻辑不变

3. **files集合字段**：
   - 现有`ad_required_count`字段保持不变
   - 修复代码中错误的`adRequiredCount`字段名

### 代码规范遵循
1. **API文件解耦**：
   - `detailAdApi.js` - 仅供详情页使用
   - `homeAdApi.js` - 仅供首页使用
   - 保持现有`systemConfigApi.js`用于搜索页

2. **工具函数解耦**：
   - `detailAdHelpers.js` - 详情页专用广告工具
   - 按页面功能独立封装，避免通用工具

3. **组件命名规范**：
   - `home-banner-ad` - 首页专用Banner广告组件
   - `detail-native-ad` - 详情页专用原生广告组件
   - 使用短横线命名法（kebab-case）

4. **模块导入方式**：
   - 使用 `require()` 而非 `import`
   - 使用 `module.exports` 导出
   - 保持与现有代码风格一致



### 重点注意事项
1. **向后兼容**：不破坏现有30%的实现
2. **问题导向**：重点解决硬编码和字段名问题
3. **用户优先**：广告失败时不阻断用户下载
4. **配置灵活**：支持快速开启/关闭广告位
5. **解耦原则**：严格按照项目解耦规则实施

## 📝 总结

本技术方案专注于实现广告展示和激励视频下载的核心功能，通过独立的ads集合管理广告配置，预计4周完成开发。

### 主要实现内容
1. **6个广告位**：开屏、首页底部、详情页中间、激励视频、搜索页顶部、列表页插入
2. **多次观看机制**：支持每个文件设置不同的广告观看次数
3. **配置动态化**：解决硬编码问题，支持后台开关控制
4. **错误降级**：广告失败时的用户友好处理

### 预期效果
- **激励视频广告**：从70%提升到95%完成度
- **页面展示广告**：从0%提升到90%完成度
- **整体完成度**：从30%提升到85%完成度
