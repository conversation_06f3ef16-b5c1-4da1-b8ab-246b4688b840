// 系统配置路由
const express = require('express')
const router = express.Router()
const configController = require('../controllers/configController')

// 获取所有配置
router.get('/', configController.getAllConfigs)

// 获取单个配置
router.get('/:key', configController.getConfig)

// 更新单个配置
router.put('/:key', configController.updateConfig)

// 批量更新配置
router.post('/batch', configController.batchUpdateConfigs)

// 创建新配置
router.post('/', configController.createConfig)

// 删除配置
router.delete('/:key', configController.deleteConfig)

module.exports = router
