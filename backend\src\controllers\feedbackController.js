// 用户反馈控制器
const feedbackService = require('../services/feedbackService')
const Joi = require('joi')

// 参数验证模式
const schemas = {
  // 获取反馈列表参数验证
  getFeedbackList: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    pageSize: Joi.number().integer().min(1).max(100).default(20),
    status: Joi.string().valid('', '待处理', '处理中', '已解决', '已关闭').default(''),
    type: Joi.string().valid('', '功能异常', '功能建议', '内容问题', '其他问题').default(''),
    keyword: Joi.string().allow('').default(''),
    startDate: Joi.string().allow('').default(''),
    endDate: Joi.string().allow('').default(''),
    sortBy: Joi.string().valid('created_time', 'feedback_status', 'feedback_type').default('created_time'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  }),

  // 更新状态参数验证
  updateStatus: Joi.object({
    status: Joi.string().valid('待处理', '处理中', '已解决', '已关闭').required()
  }),

  // 批量操作参数验证
  batchOperation: Joi.object({
    feedbackIds: Joi.array().items(Joi.string().required()).min(1).required(),
    status: Joi.string().valid('待处理', '处理中', '已解决', '已关闭').optional()
  })
}

class FeedbackController {
  // 获取反馈列表
  async getFeedbackList(req, res) {
    try {
      // 参数验证
      const { error, value } = schemas.getFeedbackList.validate(req.query)
      if (error) {
        return res.status(400).json({
          success: false,
          error: '参数验证失败: ' + error.details[0].message
        })
      }

      const result = await feedbackService.getFeedbackList(value)
      
      if (result.success) {
        res.json(result)
      } else {
        res.status(500).json(result)
      }
    } catch (error) {
      console.error('获取反馈列表失败:', error)
      res.status(500).json({
        success: false,
        error: '服务器内部错误'
      })
    }
  }

  // 获取反馈详情
  async getFeedbackDetail(req, res) {
    try {
      const { id } = req.params
      
      if (!id) {
        return res.status(400).json({
          success: false,
          error: '反馈ID不能为空'
        })
      }

      const result = await feedbackService.getFeedbackDetail(id)
      
      if (result.success) {
        res.json(result)
      } else {
        res.status(404).json(result)
      }
    } catch (error) {
      console.error('获取反馈详情失败:', error)
      res.status(500).json({
        success: false,
        error: '服务器内部错误'
      })
    }
  }

  // 更新反馈状态
  async updateFeedbackStatus(req, res) {
    try {
      const { id } = req.params
      
      if (!id) {
        return res.status(400).json({
          success: false,
          error: '反馈ID不能为空'
        })
      }

      // 参数验证
      const { error, value } = schemas.updateStatus.validate(req.body)
      if (error) {
        return res.status(400).json({
          success: false,
          error: '参数验证失败: ' + error.details[0].message
        })
      }

      const result = await feedbackService.updateFeedbackStatus(id, value.status)
      
      if (result.success) {
        res.json(result)
      } else {
        res.status(500).json(result)
      }
    } catch (error) {
      console.error('更新反馈状态失败:', error)
      res.status(500).json({
        success: false,
        error: '服务器内部错误'
      })
    }
  }

  // 批量更新反馈状态
  async batchUpdateStatus(req, res) {
    try {
      // 参数验证
      const { error, value } = schemas.batchOperation.validate(req.body)
      if (error) {
        return res.status(400).json({
          success: false,
          error: '参数验证失败: ' + error.details[0].message
        })
      }

      if (!value.status) {
        return res.status(400).json({
          success: false,
          error: '状态参数不能为空'
        })
      }

      const result = await feedbackService.batchUpdateStatus(value.feedbackIds, value.status)
      
      if (result.success) {
        res.json(result)
      } else {
        res.status(500).json(result)
      }
    } catch (error) {
      console.error('批量更新反馈状态失败:', error)
      res.status(500).json({
        success: false,
        error: '服务器内部错误'
      })
    }
  }

  // 删除反馈
  async deleteFeedback(req, res) {
    try {
      const { id } = req.params
      
      if (!id) {
        return res.status(400).json({
          success: false,
          error: '反馈ID不能为空'
        })
      }

      const result = await feedbackService.deleteFeedback(id)
      
      if (result.success) {
        res.json(result)
      } else {
        res.status(500).json(result)
      }
    } catch (error) {
      console.error('删除反馈失败:', error)
      res.status(500).json({
        success: false,
        error: '服务器内部错误'
      })
    }
  }

  // 批量删除反馈
  async batchDeleteFeedback(req, res) {
    try {
      // 参数验证
      const { error, value } = schemas.batchOperation.validate(req.body)
      if (error) {
        return res.status(400).json({
          success: false,
          error: '参数验证失败: ' + error.details[0].message
        })
      }

      const result = await feedbackService.batchDeleteFeedback(value.feedbackIds)
      
      if (result.success) {
        res.json(result)
      } else {
        res.status(500).json(result)
      }
    } catch (error) {
      console.error('批量删除反馈失败:', error)
      res.status(500).json({
        success: false,
        error: '服务器内部错误'
      })
    }
  }

  // 获取反馈统计数据
  async getFeedbackStats(req, res) {
    try {
      const result = await feedbackService.getFeedbackStats()
      
      if (result.success) {
        res.json(result)
      } else {
        res.status(500).json(result)
      }
    } catch (error) {
      console.error('获取反馈统计失败:', error)
      res.status(500).json({
        success: false,
        error: '服务器内部错误'
      })
    }
  }

  // 导出反馈数据
  async exportFeedback(req, res) {
    try {
      // 获取所有反馈数据
      const result = await feedbackService.getFeedbackList({
        page: 1,
        pageSize: 10000, // 获取所有数据
        ...req.query
      })

      if (!result.success) {
        return res.status(500).json(result)
      }

      // 生成CSV内容
      const csvHeader = '反馈ID,反馈类型,反馈标题,反馈内容,用户邮箱,状态,创建时间\n'
      const csvContent = result.data.feedbacks.map(feedback => {
        return [
          feedback._id,
          feedback.feedback_type,
          `"${feedback.feedback_title.replace(/"/g, '""')}"`,
          `"${feedback.feedback_content.replace(/"/g, '""')}"`,
          feedback.user_email || '',
          feedback.feedback_status,
          new Date(feedback.created_time).toLocaleString('zh-CN')
        ].join(',')
      }).join('\n')

      const csv = csvHeader + csvContent

      // 设置响应头
      res.setHeader('Content-Type', 'text/csv; charset=utf-8')
      res.setHeader('Content-Disposition', `attachment; filename="feedback_export_${new Date().toISOString().split('T')[0]}.csv"`)
      
      // 添加BOM以支持Excel正确显示中文
      res.write('\uFEFF')
      res.end(csv)
    } catch (error) {
      console.error('导出反馈数据失败:', error)
      res.status(500).json({
        success: false,
        error: '服务器内部错误'
      })
    }
  }
}

module.exports = new FeedbackController()
