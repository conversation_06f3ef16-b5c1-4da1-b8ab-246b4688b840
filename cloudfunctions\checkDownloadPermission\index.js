// 下载权限检查云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { file_id } = event

  try {
    // 验证参数
    if (!file_id) {
      return {
        success: false,
        error: 'invalid_params',
        message: '缺少文件ID参数'
      }
    }

    // 获取用户openid
    const wxContext = cloud.getWXContext()
    const OPENID = wxContext.OPENID

    if (!OPENID) {
      return {
        success: false,
        error: 'no_user_context',
        message: '无法获取用户身份'
      }
    }

    // 1. 检查文件是否存在
    const fileExists = await checkFileExists(file_id)
    if (!fileExists) {
      return {
        success: false,
        error: 'file_not_found',
        message: '文件不存在'
      }
    }

    // 2. 获取系统配置
    const configs = await getDownloadConfigs()

    // 3. 检查下载频率限制
    const frequencyCheck = await checkDownloadFrequency(OPENID, file_id, configs)
    if (!frequencyCheck.allowed) {
      return {
        success: false,
        error: 'frequency_limit',
        message: frequencyCheck.message
      }
    }

    // 4. 检查用户每日下载限制
    const dailyCheck = await checkDailyLimit(OPENID, configs)
    if (!dailyCheck.allowed) {
      return {
        success: false,
        error: 'daily_limit',
        message: dailyCheck.message
      }
    }

    // 5. 安全检测
    if (configs.download_security_enabled) {
      const securityCheck = await checkSecurity(OPENID, configs)
      if (!securityCheck.allowed) {
        return {
          success: false,
          error: 'security_check',
          message: securityCheck.message
        }
      }
    }

    return {
      success: true,
      message: '允许下载'
    }

  } catch (error) {
    console.error('检查下载权限失败:', error)
    return {
      success: false,
      error: 'check_failed',
      message: '权限检查失败'
    }
  }
}

// 检查文件是否存在
async function checkFileExists(fileId) {
  try {
    const result = await db.collection('files').doc(fileId).get()
    return result.data && result.data.status === 'active'
  } catch (error) {
    console.error('检查文件存在性失败:', error)
    return false
  }
}

// 获取下载配置
async function getDownloadConfigs() {
  try {
    const result = await db.collection('system_configs')
      .where({
        category: 'download'
      })
      .get()

    const configs = {}
    result.data.forEach(config => {
      let value = config.value

      switch (config.type) {
        case 'number':
          value = parseInt(value)
          break
        case 'boolean':
          value = value === 'true'
          break
        case 'json':
          value = JSON.parse(value)
          break
      }

      configs[config.key] = value
    })

    return configs
  } catch (error) {
    console.error('获取下载配置失败:', error)
    return getDefaultConfigs()
  }
}

// 获取默认配置
function getDefaultConfigs() {
  return {
    download_frequency_same_file_per_minute: 1,
    download_frequency_same_file_per_hour: 3,
    download_frequency_same_file_per_day: 10,
    download_user_total_per_hour: 10,
    download_user_total_per_day: 10,
    download_security_enabled: true,
    download_suspicious_threshold: 70
  }
}

// 检查下载频率
async function checkDownloadFrequency(openid, fileId, configs) {
  try {
    const now = new Date()

    // 检查1分钟内同文件下载次数
    const oneMinuteAgo = new Date(now.getTime() - 60 * 1000)
    const minuteCount = await db.collection('download_records')
      .where({
        openid: openid,
        file_id: fileId,
        download_time: db.command.gte(oneMinuteAgo)
      })
      .count()

    if (minuteCount.total >= configs.download_frequency_same_file_per_minute) {
      return {
        allowed: false,
        message: '该文件1分钟内下载次数已达上限'
      }
    }

    // 检查1小时内同文件下载次数
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
    const hourCount = await db.collection('download_records')
      .where({
        openid: openid,
        file_id: fileId,
        download_time: db.command.gte(oneHourAgo)
      })
      .count()

    if (hourCount.total >= configs.download_frequency_same_file_per_hour) {
      return {
        allowed: false,
        message: '该文件1小时内下载次数已达上限'
      }
    }

    return { allowed: true }

  } catch (error) {
    console.error('检查下载频率失败:', error)
    return { allowed: true } // 降级处理
  }
}

// 检查每日下载限制（最近24小时）
async function checkDailyLimit(openid, configs) {
  try {
    const now = new Date()
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000)

    const dailyCount = await db.collection('download_records')
      .where({
        openid: openid,
        download_time: db.command.gte(last24Hours)
      })
      .count()

    if (dailyCount.total >= configs.download_user_total_per_day) {
      return {
        allowed: false,
        message: `最近24小时下载次数已达上限（${configs.download_user_total_per_day}次）`
      }
    }

    return { allowed: true }

  } catch (error) {
    console.error('检查每日限制失败:', error)
    return { allowed: true } // 降级处理
  }
}

// 安全检测
async function checkSecurity(openid, configs) {
  try {
    // 计算可疑评分
    const score = await calculateSuspiciousScore(openid)
    
    if (score >= configs.download_suspicious_threshold) {
      return {
        allowed: false,
        message: '检测到异常行为，暂时无法下载'
      }
    }

    return { allowed: true }

  } catch (error) {
    console.error('安全检测失败:', error)
    return { allowed: true } // 降级处理
  }
}

// 计算可疑评分
async function calculateSuspiciousScore(openid) {
  let score = 0
  const now = new Date()
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)

  try {
    // 检查1小时内下载频率
    const hourlyDownloads = await db.collection('download_records')
      .where({
        openid: openid,
        download_time: db.command.gte(oneHourAgo)
      })
      .count()

    if (hourlyDownloads.total > 15) {
      score += 30
    }

    // 检查重复下载
    const recentDownloads = await db.collection('download_records')
      .where({
        openid: openid,
        download_time: db.command.gte(oneHourAgo)
      })
      .get()

    const fileIds = recentDownloads.data.map(record => record.file_id)
    const uniqueFiles = new Set(fileIds)
    const repeatCount = fileIds.length - uniqueFiles.size

    if (repeatCount > 3) {
      score += 25
    }

    return score

  } catch (error) {
    console.error('计算可疑评分失败:', error)
    return 0
  }
}
