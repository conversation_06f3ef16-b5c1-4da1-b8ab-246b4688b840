// 用户授权管理器
class AuthManager {
  constructor() {
    this.isAuthorized = false
    this.userInfo = null
    this.openid = null
  }

  // 检查用户授权状态
  async checkAuthStatus() {
    try {
      // 检查是否已经有用户信息
      const userInfo = wx.getStorageSync('userInfo')
      const openid = wx.getStorageSync('user_openid')
      
      if (userInfo && openid && !openid.startsWith('device_')) {
        this.isAuthorized = true
        this.userInfo = userInfo
        this.openid = openid
        return true
      }
      
      return false
    } catch (error) {
      console.error('检查授权状态失败:', error)
      return false
    }
  }

  // 静默登录（通过wx.login + 云函数）
  async silentLogin() {
    try {
      console.log('🔄 开始静默登录...')
      
      // 直接调用云函数 - 这是最简单的标准方法

      // 3. 直接调用云函数获取openid（标准方法）
      console.log('☁️ 调用云函数获取openid...')
      const result = await wx.cloud.callFunction({
        name: 'getOpenId',
        data: {}
      })

      console.log('云函数调用结果:', result)

      if (result.result && result.result.success && result.result.openid) {
        this.openid = result.result.openid
        this.isAuthorized = true

        // 缓存openid
        wx.setStorageSync('user_openid', this.openid)

        console.log('✅ 静默登录成功:', this.openid)
        return {
          success: true,
          openid: this.openid
        }
      } else {
        console.log('⚠️ 云函数未返回有效openid:', result.result)
        throw new Error('云函数未返回有效openid')
      }

    } catch (error) {
      console.error('静默登录失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 用户授权登录（获取用户信息）
  async authorizeLogin() {
    try {
      console.log('🔄 开始用户授权登录...')

      // 1. 先进行静默登录获取openid
      const silentResult = await this.silentLogin()
      if (!silentResult.success) {
        throw new Error('静默登录失败: ' + silentResult.error)
      }

      // 2. 获取用户信息
      const userProfile = await new Promise((resolve, reject) => {
        wx.getUserProfile({
          desc: '用于完善用户资料',
          success: resolve,
          fail: reject
        })
      })

      console.log('✅ 获取用户信息成功:', userProfile.userInfo)

      this.userInfo = userProfile.userInfo
      this.isAuthorized = true

      // 缓存用户信息
      wx.setStorageSync('userInfo', this.userInfo)

      return {
        success: true,
        openid: this.openid,
        userInfo: this.userInfo
      }

    } catch (error) {
      console.error('用户授权登录失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 获取用户openid（优先级：缓存 > 静默登录 > 设备ID）
  async getOpenId() {
    try {
      // 1. 检查缓存
      if (this.openid) {
        return { success: true, openid: this.openid, method: 'cache' }
      }

      // 2. 检查本地存储
      const cachedOpenId = wx.getStorageSync('user_openid')
      if (cachedOpenId && !cachedOpenId.startsWith('device_')) {
        this.openid = cachedOpenId
        return { success: true, openid: this.openid, method: 'storage' }
      }

      // 3. 尝试静默登录
      const silentResult = await this.silentLogin()
      if (silentResult.success) {
        return { success: true, openid: silentResult.openid, method: 'silent_login' }
      }

      // 4. 返回失败，使用设备ID
      return {
        success: false,
        error: 'no_openid_available',
        message: '无法获取真实openid，建议使用设备ID'
      }

    } catch (error) {
      console.error('获取openid失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 检查是否需要用户授权
  needUserAuthorization() {
    return !this.isAuthorized || !this.userInfo
  }

  // 清除授权信息
  clearAuth() {
    this.isAuthorized = false
    this.userInfo = null
    this.openid = null
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('user_openid')
  }

  // 获取用户显示信息
  getUserDisplayInfo() {
    if (this.userInfo) {
      return {
        nickName: this.userInfo.nickName,
        avatarUrl: this.userInfo.avatarUrl,
        hasAuth: true
      }
    }
    return {
      nickName: '未授权用户',
      avatarUrl: '',
      hasAuth: false
    }
  }
}

// 创建全局实例
const authManager = new AuthManager()

module.exports = {
  authManager,
  AuthManager
}
