// 下载管理器 - 防刷核心逻辑
const { downloadConfigManager } = require('./downloadConfigManager')

class DownloadManager {
  constructor() {
    this.configManager = downloadConfigManager
    this.activeDownloads = new Map() // 活跃下载记录
  }

  // 检查下载权限（核心防刷逻辑）
  async checkDownloadPermission(fileId, openid) {
    try {
      // 1. 获取相关配置
      const configs = await this.configManager.getBatchConfigs([
        'download_frequency_same_file_per_minute',
        'download_frequency_same_file_per_hour',
        'download_frequency_same_file_per_day',
        'download_user_total_per_hour',
        'download_user_total_per_day',
        'download_max_concurrent',
        'download_security_enabled'
      ])

      // 2. 检查并发下载限制
      const concurrentCheck = this.checkConcurrentDownloads(openid, configs.download_max_concurrent)
      if (!concurrentCheck.allowed) {
        return concurrentCheck
      }

      // 3. 检查同文件下载频率
      const sameFileCheck = await this.checkSameFileFrequency(fileId, openid, {
        per_minute: configs.download_frequency_same_file_per_minute,
        per_hour: configs.download_frequency_same_file_per_hour,
        per_day: configs.download_frequency_same_file_per_day
      })
      if (!sameFileCheck.allowed) {
        return sameFileCheck
      }

      // 4. 检查用户总下载频率
      const userTotalCheck = await this.checkUserTotalFrequency(openid, {
        per_hour: configs.download_user_total_per_hour,
        per_day: configs.download_user_total_per_day
      })
      if (!userTotalCheck.allowed) {
        return userTotalCheck
      }

      // 5. 安全检测（如果启用）
      if (configs.download_security_enabled) {
        const securityCheck = await this.checkDownloadSecurity(fileId, openid)
        if (!securityCheck.allowed) {
          return securityCheck
        }
      }

      return { allowed: true, message: '允许下载' }

    } catch (error) {
      console.error('检查下载权限失败:', error)
      return {
        allowed: false,
        reason: 'check_failed',
        message: '权限检查失败，请重试'
      }
    }
  }

  // 检查并发下载限制
  checkConcurrentDownloads(openid, maxConcurrent) {
    const userDownloads = Array.from(this.activeDownloads.values())
      .filter(download => download.openid === openid)

    if (userDownloads.length >= maxConcurrent) {
      return {
        allowed: false,
        reason: 'concurrent_limit',
        message: `同时下载数量已达上限（${maxConcurrent}个）`
      }
    }

    return { allowed: true }
  }

  // 检查同文件下载频率
  async checkSameFileFrequency(fileId, openid, limits) {
    try {
      const db = wx.cloud.database()
      const now = new Date()

      // 检查1分钟内下载次数
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000)
      const minuteCount = await db.collection('download_records')
        .where({
          openid: openid,
          file_id: fileId,
          download_time: db.command.gte(oneMinuteAgo)
        })
        .count()

      if (minuteCount.total >= limits.per_minute) {
        return {
          allowed: false,
          reason: 'minute_limit',
          message: '该文件1分钟内下载次数已达上限，请稍后再试'
        }
      }

      // 检查1小时内下载次数
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
      const hourCount = await db.collection('download_records')
        .where({
          openid: openid,
          file_id: fileId,
          download_time: db.command.gte(oneHourAgo)
        })
        .count()

      if (hourCount.total >= limits.per_hour) {
        return {
          allowed: false,
          reason: 'hour_limit',
          message: '该文件1小时内下载次数已达上限'
        }
      }

      // 检查1天内下载次数
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      const dayCount = await db.collection('download_records')
        .where({
          openid: openid,
          file_id: fileId,
          download_time: db.command.gte(oneDayAgo)
        })
        .count()

      if (dayCount.total >= limits.per_day) {
        return {
          allowed: false,
          reason: 'day_limit',
          message: '该文件今日下载次数已达上限'
        }
      }

      return { allowed: true }

    } catch (error) {
      console.error('检查同文件频率失败:', error)
      return { allowed: true } // 降级处理，允许下载
    }
  }

  // 检查用户总下载频率
  async checkUserTotalFrequency(openid, limits) {
    try {
      const db = wx.cloud.database()
      const now = new Date()

      // 检查1小时内总下载次数
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
      const hourCount = await db.collection('download_records')
        .where({
          openid: openid,
          download_time: db.command.gte(oneHourAgo)
        })
        .count()

      if (hourCount.total >= limits.per_hour) {
        return {
          allowed: false,
          reason: 'user_hour_limit',
          message: `您1小时内下载次数已达上限（${limits.per_hour}次）`
        }
      }

      // 检查1天内总下载次数
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      const dayCount = await db.collection('download_records')
        .where({
          openid: openid,
          download_time: db.command.gte(oneDayAgo)
        })
        .count()

      if (dayCount.total >= limits.per_day) {
        return {
          allowed: false,
          reason: 'user_day_limit',
          message: `您今日下载次数已达上限（${limits.per_day}次）`
        }
      }

      return { allowed: true }

    } catch (error) {
      console.error('检查用户总频率失败:', error)
      return { allowed: true } // 降级处理，允许下载
    }
  }

  // 安全检测
  async checkDownloadSecurity(fileId, openid) {
    try {
      // 获取可疑行为阈值
      const threshold = await this.configManager.getConfig('download_suspicious_threshold', 70)

      // 计算可疑评分
      const suspiciousScore = await this.calculateSuspiciousScore(openid)

      if (suspiciousScore >= threshold) {
        return {
          allowed: false,
          reason: 'suspicious_behavior',
          message: '检测到异常行为，暂时无法下载'
        }
      }

      return { allowed: true }

    } catch (error) {
      console.error('安全检测失败:', error)
      return { allowed: true } // 降级处理，允许下载
    }
  }

  // 计算可疑行为评分
  async calculateSuspiciousScore(openid) {
    let score = 0
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)

    try {
      const db = wx.cloud.database()

      // 1. 检查1小时内下载频率 (+30分)
      const hourlyDownloads = await db.collection('download_records')
        .where({
          openid: openid,
          download_time: db.command.gte(oneHourAgo)
        })
        .count()

      if (hourlyDownloads.total > 15) {
        score += 30
      }

      // 2. 检查重复下载同一文件 (+25分)
      const recentDownloads = await db.collection('download_records')
        .where({
          openid: openid,
          download_time: db.command.gte(oneHourAgo)
        })
        .get()

      const fileIds = recentDownloads.data.map(record => record.file_id)
      const uniqueFiles = new Set(fileIds)
      const repeatCount = fileIds.length - uniqueFiles.size

      if (repeatCount > 3) {
        score += 25
      }

      // 3. 检查设备信息变化 (+20分)
      const deviceChanges = await this.checkDeviceChanges(openid)
      if (deviceChanges > 2) {
        score += 20
      }

      return score

    } catch (error) {
      console.error('计算可疑评分失败:', error)
      return 0
    }
  }

  // 检查设备变化
  async checkDeviceChanges(openid) {
    try {
      const db = wx.cloud.database()
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)

      const recentRecords = await db.collection('download_records')
        .where({
          openid: openid,
          download_time: db.command.gte(oneHourAgo)
        })
        .get()

      const devices = new Set()
      recentRecords.data.forEach(record => {
        if (record.device_info && record.device_info.model) {
          devices.add(record.device_info.model)
        }
      })

      return devices.size

    } catch (error) {
      console.error('检查设备变化失败:', error)
      return 0
    }
  }

  // 记录下载开始
  recordDownloadStart(fileId, openid) {
    const downloadId = `${openid}_${fileId}_${Date.now()}`
    this.activeDownloads.set(downloadId, {
      fileId,
      openid,
      startTime: Date.now()
    })
    return downloadId
  }

  // 记录下载完成
  recordDownloadComplete(downloadId) {
    this.activeDownloads.delete(downloadId)
  }

  // 获取下载统计
  async getDownloadStats(openid) {
    try {
      const configs = await this.configManager.getBatchConfigs([
        'download_user_total_per_day',
        'download_max_concurrent'
      ])

      const db = wx.cloud.database()
      const now = new Date()
      const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000)

      // 直接查询最近24小时的用户记录
      const recentRecords = await db.collection('download_records')
        .where({
          openid: openid,
          download_time: db.command.gte(last24Hours)
        })
        .get()

      const todayCount = recentRecords.data.length
      const currentDownloads = Array.from(this.activeDownloads.values())
        .filter(download => download.openid === openid).length

      return {
        todayCount: todayCount,
        dailyLimit: configs.download_user_total_per_day,
        remaining: Math.max(0, configs.download_user_total_per_day - todayCount),
        currentDownloads: currentDownloads,
        maxConcurrent: configs.download_max_concurrent
      }

    } catch (error) {
      console.error('获取下载统计失败:', error)
      return {
        todayCount: 0,
        dailyLimit: 10,
        remaining: 10,
        currentDownloads: 0,
        maxConcurrent: 1
      }
    }
  }
}

// 创建全局实例
const downloadManager = new DownloadManager()

module.exports = {
  downloadManager,
  DownloadManager
}
